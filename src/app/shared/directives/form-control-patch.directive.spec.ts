import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Component, DebugElement } from '@angular/core';
import { FormControlPatchDirective } from './form-control-patch.directive';
import { By } from '@angular/platform-browser';
import { FormControl, NgControl } from '@angular/forms';

@Component({
	template: `<input formControlName="testControl" />`,
	imports: [FormControlPatchDirective],
})
class TestComponent {}

describe('FormControlPatchDirective', () => {
	let directive: FormControlPatchDirective;
	let fixture: ComponentFixture<TestComponent>;
	let inputElement: DebugElement;

	beforeEach(() => {
		TestBed.configureTestingModule({
			imports: [FormControlPatchDirective, TestComponent],
			providers: [
				{
					provide: NgControl,
					useClass: class NgControlMock {
						control = new FormControl();
					},
				},
			],
		});

		fixture = TestBed.createComponent(TestComponent);
		inputElement = fixture.debugElement.query(By.css('input'));
		directive = inputElement.injector.get(FormControlPatchDirective) as FormControlPatchDirective;
		fixture.detectChanges();
	});

	it('should create an instance', () => {
		expect(directive).toBeTruthy();
	});

	it('should trigger blur event and update control value', () => {
		const ngControl = TestBed.inject(NgControl);
		spyOn<any, any>(ngControl.control, 'setValue');

		inputElement.triggerEventHandler('blur', null);

		expect(ngControl.control?.setValue).toHaveBeenCalled();
	});

	it('should not update control value if it has value', () => {
		const ngControl = { control: new FormControl('test') };
		spyOn(ngControl.control, 'setValue');

		TestBed.resetTestingModule();
		TestBed.configureTestingModule({
			imports: [FormControlPatchDirective, TestComponent],
			providers: [{ provide: NgControl, useValue: ngControl }],
		});

		const testFixture = TestBed.createComponent(TestComponent);
		const testInputElement = testFixture.debugElement.query(By.css('input'));
		testFixture.detectChanges();

		testInputElement.triggerEventHandler('blur', null);

		expect(ngControl.control.setValue).not.toHaveBeenCalled();
	});
});
