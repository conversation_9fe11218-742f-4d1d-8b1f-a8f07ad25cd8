import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';

@Injectable({
	providedIn: 'root',
})
export class ApiService {
	private readonly baseUrl = '/prod-api';

	constructor(private readonly http: HttpClient) {}

	// GET
	getData<T>(endpoint: string, params?: any): Observable<T> {
		let httpParams = new HttpParams();
		if (params) {
			Object.keys(params).forEach((key) => {
				httpParams = httpParams.set(key, params[key]);
			});
		}

		return this.http.get<T>(`${this.baseUrl}/${endpoint}`, { params: httpParams });
	}

	// POST
	postData<T>(endpoint: string, data: any): Observable<T> {
		return this.http.post<T>(`${this.baseUrl}/${endpoint}`, data);
	}

	// PUT
	updateData<T>(endpoint: string, data: any): Observable<T> {
		return this.http.put<T>(`${this.baseUrl}/${endpoint}`, data);
	}

	// DELETE
	deleteData<T>(endpoint: string): Observable<T> {
		return this.http.delete<T>(`${this.baseUrl}/${endpoint}`);
	}
}
