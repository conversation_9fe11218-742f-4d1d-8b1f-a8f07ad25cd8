import { ChangeDetectionStrategy, ChangeDetectorRef, Component, NO_ERRORS_SCHEMA, OnInit } from '@angular/core';
import { ActivatedRoute, NavigationEnd, Router, RouterLink, RouterLinkActive } from '@angular/router';
import { filter } from 'rxjs';
import { MatIconModule } from '@angular/material/icon';
import { DestroyRefComponent } from '@shared/components/destroy-observable/destroy-ref.component';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { OverlayModule } from '@angular/cdk/overlay';
import { environment } from '@environments/environment';
import { AuthService } from '@shared/auth/auth.service';
import { NotificationService } from '@shared/services/notification.service';

@Component({
	selector: 'iata-main-header',
	templateUrl: './main-header.component.html',
	styleUrls: ['./main-header.component.scss'],
	changeDetection: ChangeDetectionStrategy.OnPush,
	imports: [MatIconModule, MatTooltipModule, RouterLink, RouterLinkActive, TranslateModule, OverlayModule],
	schemas: [NO_ERRORS_SCHEMA],
})
export class MainHeaderComponent extends DestroyRefComponent implements OnInit {
	isOpen = false;
	currentUser = '';
	userList: { userId: string; orgId: string }[] = [];

	constructor(
		private readonly router: Router,
		private readonly route: ActivatedRoute,
		private readonly cdr: ChangeDetectorRef,
		private readonly authService: AuthService,
		private readonly notificationService: NotificationService,
		private readonly translate: TranslateService
	) {
		super();
	}

	ngOnInit(): void {
		this.currentUser = localStorage.getItem('currentUser') ?? environment.userId;
		this.userList = environment.userList;

		this.router.events.pipe(
			filter((event) => event instanceof NavigationEnd),
			takeUntilDestroyed(this.destroyRef)
		);
	}

	onSwitch(user: { userId: string; orgId: string }): void {
		localStorage.removeItem('currentUser');
		this.authService.logout();
		this.authService
			.login({
				userId: user.userId,
				orgId: user.orgId,
			})
			.subscribe({
				next: () => {
					localStorage.setItem('currentUser', user.userId);
					this.currentUser = user.userId;
					this.isOpen = false;
					this.cdr.markForCheck();
					this.notificationService.showSuccess(`${user.userId} ` + this.translate.instant('common.mainHeader.userlogin.success'));
					if (user.userId === 'shipper1') {
						this.router.navigate(['sli'], { relativeTo: this.route });
					} else if (user.userId === 'forwarder1') {
						this.router.navigate(['hawb'], { relativeTo: this.route });
					}
				},
				error: (err) => {
					this.notificationService.showError(
						`${user.userId} ` + this.translate.instant('common.mainHeader.userlogin.fail') + `: ${err.message}`
					);
				},
			});
	}
}
