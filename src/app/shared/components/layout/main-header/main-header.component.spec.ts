import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MainHeaderComponent } from './main-header.component';
import { TranslateModule } from '@ngx-translate/core';
import { CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { AuthService } from '@shared/auth/auth.service';
import { NotificationService } from '@shared/services/notification.service';
import { of, throwError } from 'rxjs';
import { By } from '@angular/platform-browser';
import { OverlayModule } from '@angular/cdk/overlay';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';

describe('MainHeaderComponent', () => {
	let component: MainHeaderComponent;
	let fixture: ComponentFixture<MainHeaderComponent>;
	let mockRouter: jasmine.SpyObj<Router>;
	let mockAuthService: jasmine.SpyObj<AuthService>;
	let mockNotificationService: jasmine.SpyObj<NotificationService>;

	beforeEach(async () => {
		mockRouter = jasmine.createSpyObj('Router', ['navigate']);
		mockAuthService = jasmine.createSpyObj('AuthService', ['login', 'logout']);
		mockNotificationService = jasmine.createSpyObj('NotificationService', ['showSuccess', 'showError']);

		await TestBed.configureTestingModule({
			imports: [TranslateModule.forRoot(), MatIconModule, MatTooltipModule, OverlayModule],
			providers: [
				{ provide: Router, useValue: mockRouter },
				{ provide: ActivatedRoute, useValue: { snapshot: {} } },
				{ provide: AuthService, useValue: mockAuthService },
				{ provide: NotificationService, useValue: mockNotificationService },
			],
			schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA],
		}).compileComponents();

		await TestBed.overrideComponent(MainHeaderComponent, {
			set: {
				schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA],
			},
		});
	});

	beforeEach(() => {
		fixture = TestBed.createComponent(MainHeaderComponent);
		component = fixture.componentInstance;

		// Mock localStorage
		spyOn(localStorage, 'getItem').and.returnValue('testUser');
		spyOn(localStorage, 'setItem');
		spyOn(localStorage, 'removeItem');

		// Set up test data
		component.userList = [
			{ userId: 'shipper1', orgId: 'org1' },
			{ userId: 'forwarder1', orgId: 'org2' },
		];

		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	it('should initialize with current user from localStorage', () => {
		expect(component.currentUser).toBe('testUser');
	});

	it('should toggle dropdown when user icon is clicked', () => {
		expect(component.isOpen).toBeFalse();

		const userIcon = fixture.debugElement.query(By.css('.user-toggle'));
		// eslint-disable-next-line @typescript-eslint/no-empty-function
		userIcon.triggerEventHandler('click', { stopPropagation: () => {} });

		expect(component.isOpen).toBeTrue();

		// eslint-disable-next-line @typescript-eslint/no-empty-function
		userIcon.triggerEventHandler('click', { stopPropagation: () => {} });

		expect(component.isOpen).toBeFalse();
	});

	it('should switch user successfully', () => {
		const testUser = { userId: 'shipper1', orgId: 'org1' };
		mockAuthService.login.and.returnValue(of({}));

		component.onSwitch(testUser);

		expect(localStorage.removeItem).toHaveBeenCalledWith('currentUser');
		expect(mockAuthService.logout).toHaveBeenCalled();
		expect(mockAuthService.login).toHaveBeenCalledWith(testUser);
		expect(localStorage.setItem).toHaveBeenCalledWith('currentUser', 'shipper1');
		expect(component.currentUser).toBe('shipper1');
		expect(component.isOpen).toBeFalse();
		expect(mockNotificationService.showSuccess).toHaveBeenCalled();
		expect(mockRouter.navigate).toHaveBeenCalledWith(['sli'], { relativeTo: jasmine.any(Object) });
	});

	it('should navigate to hawb for forwarder user', () => {
		const testUser = { userId: 'forwarder1', orgId: 'org2' };
		mockAuthService.login.and.returnValue(of({}));

		component.onSwitch(testUser);

		expect(mockRouter.navigate).toHaveBeenCalledWith(['hawb'], { relativeTo: jasmine.any(Object) });
	});

	it('should handle login error', () => {
		const testUser = { userId: 'testUser', orgId: 'testOrg' };
		const errorMsg = 'Authentication failed';
		mockAuthService.login.and.returnValue(throwError(() => new Error(errorMsg)));

		component.onSwitch(testUser);

		expect(mockNotificationService.showError).toHaveBeenCalled();
		const errorCall = mockNotificationService.showError.calls.mostRecent();
		expect(errorCall.args[0]).toContain('testUser');
		expect(errorCall.args[0]).toContain('common.mainHeader.userlogin.fail');
		expect(errorCall.args[0]).toContain(errorMsg);
	});

	it('should display the current user ID', () => {
		const userIdElement = fixture.debugElement.query(By.css('.userid'));
		expect(userIdElement.nativeElement.textContent).toBe('testUser');
	});

	it('should render user list in dropdown', () => {
		// Open the dropdown
		component.isOpen = true;
		fixture.detectChanges();

		// Check if user list is rendered
		const userItems = fixture.debugElement.queryAll(By.css('.iata-main-header__secondary-nav__dropdown-list__item'));
		expect(userItems.length).toBe(2);

		// Check if user IDs are displayed correctly
		expect(userItems[0].query(By.css('span')).nativeElement.textContent).toBe('shipper1');
		expect(userItems[1].query(By.css('span')).nativeElement.textContent).toBe('forwarder1');
	});
});
