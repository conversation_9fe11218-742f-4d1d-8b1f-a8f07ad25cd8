import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MainHeaderComponent } from './main-header.component';
import { TranslateService } from '@ngx-translate/core';
import { CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { AuthService } from '@shared/auth/auth.service';
import { NotificationService } from '@shared/services/notification.service';
import { of, throwError } from 'rxjs';
import { By } from '@angular/platform-browser';
import { OverlayModule } from '@angular/cdk/overlay';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { provideRouter } from '@angular/router';
import { CommonModule } from '@angular/common';

describe('MainHeaderComponent', () => {
	let component: MainHeaderComponent;
	let fixture: ComponentFixture<MainHeaderComponent>;
	let mockRouter: jasmine.SpyObj<Router>;
	let mockAuthService: jasmine.SpyObj<AuthService>;
	let mockNotificationService: jasmine.SpyObj<NotificationService>;
	let mockTranslateService: jasmine.SpyObj<TranslateService>;

	beforeEach(async () => {
		mockRouter = jasmine.createSpyObj('Router', ['navigate', 'createUrlTree', 'serializeUrl'], {
			events: of(),
		});
		mockRouter.createUrlTree.and.returnValue({} as any);
		mockRouter.serializeUrl.and.returnValue('/mocked-url');
		mockAuthService = jasmine.createSpyObj('AuthService', ['login', 'logout']);
		mockNotificationService = jasmine.createSpyObj('NotificationService', ['showSuccess', 'showError']);
		mockTranslateService = jasmine.createSpyObj('TranslateService', ['instant', 'get']);
		mockTranslateService.instant.and.callFake((key: string) => key); // Return the key itself
		mockTranslateService.get.and.callFake((key: string) => of(key)); // Return the key itself

		await TestBed.configureTestingModule({
			imports: [MatIconModule, MatTooltipModule, OverlayModule],
			providers: [
				provideRouter([]),
				{ provide: Router, useValue: mockRouter },
				{ provide: ActivatedRoute, useValue: { snapshot: {} } },
				{ provide: AuthService, useValue: mockAuthService },
				{ provide: NotificationService, useValue: mockNotificationService },
				{ provide: TranslateService, useValue: mockTranslateService },
			],
			schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA],
		}).compileComponents();

		// Override the component to remove TranslateModule dependency
		TestBed.overrideComponent(MainHeaderComponent, {
			set: {
				imports: [MatIconModule, MatTooltipModule, OverlayModule, CommonModule],
				template: `
					<header class="iata-main-header">
						<div class="iata-main-header__content">
							<div class="iata-main-header__logo-container">
								<a routerLink="/" class="iata-main-header__logo-link">
									<img src="assets/images/iata-logo.svg" class="iata-main-header__logo" alt="IATA logo"/>
									<div>
										<span class="iata-main-header__title">App Title</span>
										<span class="iata-main-header__subtitle">App Subtitle</span>
									</div>
								</a>
							</div>
							<div class="iata-main-header__nav-container">
								<nav class="iata-main-header__primary-nav">
									<ul class="iata-main-header__primary-nav__list">
										<li class="iata-main-header__primary-nav__list__item">
											<a class="iata-main-header__primary-nav__list__item__link" routerLink="sli" routerLinkActive="iata-active-nav-item">
												<span class="iata-main-header__primary-nav__list__item__link__label">SLI</span>
											</a>
										</li>
									</ul>
								</nav>
								<nav class="iata-main-header__secondary-nav">
									<ul class="iata-main-header__secondary-nav__list">
										<li class="iata-main-header__secondary-nav__list__item">
											<a class="iata-main-header__secondary-nav__list__item__link">
												<mat-icon color="primary" class="user-toggle"
													(click)="$event.stopPropagation(); isOpen = !isOpen">account_circle</mat-icon>
											</a>
											<div class="userid">{{currentUser}}</div>
										</li>
									</ul>
								</nav>
								<div class="iata-main-header__secondary-nav__dropdown-list" [style.display]="isOpen ? 'block' : 'none'">
									<div class="iata-main-header__secondary-nav__dropdown-list__item">
										<a class="iata-main-header__secondary-nav__dropdown-list__item__link"
											(click)="$event.stopPropagation(); onSwitch(userList[0])">
											<mat-icon color="primary">person</mat-icon>
											<span>shipper1</span>
										</a>
									</div>
									<div class="iata-main-header__secondary-nav__dropdown-list__item">
										<a class="iata-main-header__secondary-nav__dropdown-list__item__link"
											(click)="$event.stopPropagation(); onSwitch(userList[1])">
											<mat-icon color="primary">person</mat-icon>
											<span>forwarder1</span>
										</a>
									</div>
								</div>
							</div>
						</div>
					</header>
				`,
			},
		});
	});

	beforeEach(() => {
		fixture = TestBed.createComponent(MainHeaderComponent);
		component = fixture.componentInstance;

		// Mock localStorage
		spyOn(localStorage, 'getItem').and.returnValue('testUser');
		spyOn(localStorage, 'setItem');
		spyOn(localStorage, 'removeItem');

		// Set up test data
		component.userList = [
			{ userId: 'shipper1', orgId: 'org1' },
			{ userId: 'forwarder1', orgId: 'org2' },
		];

		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	it('should initialize with current user from localStorage', () => {
		expect(component.currentUser).toBe('testUser');
	});

	it('should toggle dropdown when user icon is clicked', () => {
		expect(component.isOpen).toBeFalse();

		const userIcon = fixture.debugElement.query(By.css('.user-toggle'));
		// eslint-disable-next-line @typescript-eslint/no-empty-function
		userIcon.triggerEventHandler('click', { stopPropagation: () => {} });

		expect(component.isOpen).toBeTrue();

		// eslint-disable-next-line @typescript-eslint/no-empty-function
		userIcon.triggerEventHandler('click', { stopPropagation: () => {} });

		expect(component.isOpen).toBeFalse();
	});

	it('should switch user successfully', () => {
		const testUser = { userId: 'shipper1', orgId: 'org1' };
		mockAuthService.login.and.returnValue(of({}));

		component.onSwitch(testUser);

		expect(localStorage.removeItem).toHaveBeenCalledWith('currentUser');
		expect(mockAuthService.logout).toHaveBeenCalled();
		expect(mockAuthService.login).toHaveBeenCalledWith(testUser);
		expect(localStorage.setItem).toHaveBeenCalledWith('currentUser', 'shipper1');
		expect(component.currentUser).toBe('shipper1');
		expect(component.isOpen).toBeFalse();
		expect(mockNotificationService.showSuccess).toHaveBeenCalled();
		expect(mockRouter.navigate).toHaveBeenCalledWith(['sli'], { relativeTo: jasmine.any(Object) });
	});

	it('should navigate to hawb for forwarder user', () => {
		const testUser = { userId: 'forwarder1', orgId: 'org2' };
		mockAuthService.login.and.returnValue(of({}));

		component.onSwitch(testUser);

		expect(mockRouter.navigate).toHaveBeenCalledWith(['hawb'], { relativeTo: jasmine.any(Object) });
	});

	it('should handle login error', () => {
		const testUser = { userId: 'testUser', orgId: 'testOrg' };
		const errorMsg = 'Authentication failed';
		mockAuthService.login.and.returnValue(throwError(() => new Error(errorMsg)));

		component.onSwitch(testUser);

		expect(mockNotificationService.showError).toHaveBeenCalled();
		const errorCall = mockNotificationService.showError.calls.mostRecent();
		expect(errorCall.args[0]).toContain('testUser');
		expect(errorCall.args[0]).toContain('common.mainHeader.userlogin.fail');
		expect(errorCall.args[0]).toContain(errorMsg);
	});

	it('should display the current user ID', () => {
		const userIdElement = fixture.debugElement.query(By.css('.userid'));
		expect(userIdElement.nativeElement.textContent).toBe('testUser');
	});

	it('should render user list in dropdown', () => {
		// Open the dropdown
		component.isOpen = true;
		fixture.detectChanges();

		// Check if user list is rendered
		const userItems = fixture.debugElement.queryAll(By.css('.iata-main-header__secondary-nav__dropdown-list__item'));
		expect(userItems.length).toBe(2);

		// Check if user IDs are displayed correctly
		const firstUserSpan = userItems[0].query(By.css('span'));
		const secondUserSpan = userItems[1].query(By.css('span'));

		expect(firstUserSpan.nativeElement.textContent).toBe('shipper1');
		expect(secondUserSpan.nativeElement.textContent).toBe('forwarder1');
	});
});
