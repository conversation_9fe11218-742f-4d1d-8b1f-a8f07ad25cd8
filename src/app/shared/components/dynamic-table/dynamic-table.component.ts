import { ChangeDetectionStrategy, Component, EventEmitter, Input, OnChanges, Output, SimpleChanges } from '@angular/core';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { MatSortModule } from '@angular/material/sort';
import { MatPaginatorModule, PageEvent } from '@angular/material/paginator';

@Component({
	selector: 'iata-dynamic-table',
	templateUrl: './dynamic-table.component.html',
	styleUrls: ['./dynamic-table.component.scss'],
	changeDetection: ChangeDetectionStrategy.OnPush,
	imports: [MatTableModule, MatSortModule, MatPaginatorModule],
})
export class DynamicTableComponent implements OnChanges {
	@Input() records: any[] = [];
	@Input() headers: Record<string, string> = {};
	@Input() totalRecords = 0;

	@Output() pagination: EventEmitter<PageEvent> = new EventEmitter<PageEvent>();

	readonly tablePageSizes: number[] = [10, 50, 100];

	displayedColumns: string[] = [];
	dataSource = new MatTableDataSource<any>([]);

	ngOnChanges(changes: SimpleChanges): void {
		if (changes['records']) {
			this.displayedColumns = this.collectDisplayedColumns(this.records);
			this.dataSource.data = this.records;
		}
	}

	formatCamelCaseHeader(header: string): string {
		const splittedWords = header.replace(/([^A-Z])([A-Z])/g, '$1 $2');
		return `${splittedWords.charAt(0).toUpperCase()}${splittedWords.substring(1)}`;
	}

	onPaginationChange(event: PageEvent): void {
		this.pagination.emit(event);
	}

	private collectDisplayedColumns(records: object[]): string[] {
		const columnsSet = new Set<string>();
		records.forEach((record: object) => {
			Object.keys(record).forEach((key: string) => columnsSet.add(key));
		});
		return Array.from(columnsSet);
	}
}
