import { ComponentFixture, TestBed } from '@angular/core/testing';
import { DynamicTableComponent } from './dynamic-table.component';
import { CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA, SimpleChange } from '@angular/core';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { PageEvent } from '@angular/material/paginator';

describe('DynamicTableComponent', () => {
	let component: DynamicTableComponent;
	let fixture: ComponentFixture<DynamicTableComponent>;

	beforeEach(async () => {
		await TestBed.configureTestingModule({
			imports: [DynamicTableComponent, NoopAnimationsModule],
			schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA],
		}).compileComponents();
	});

	beforeEach(() => {
		fixture = TestBed.createComponent(DynamicTableComponent);
		component = fixture.componentInstance;
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	it('#ngOnChanges $records update should set table data source', () => {
		const records = [{ fileId: '1' }, { fileId: '2' }, { fileId: '3' }];
		spyOn<any>(component, 'collectDisplayedColumns');

		component.records = records;
		component.ngOnChanges({ records: new SimpleChange(null, records, true) });

		expect(component.dataSource.data).toEqual(records);
		expect(component['collectDisplayedColumns']).toHaveBeenCalledWith(records);
	});

	it('#formatCamelCaseHeader should capitalize first word in table column header', () => {
		const camelCaseHeader = component.formatCamelCaseHeader('some title');
		expect(camelCaseHeader).toEqual('Some title');
	});

	it('#onPaginationChange should emit the output event', () => {
		const event: PageEvent = { pageSize: 10, previousPageIndex: 0, pageIndex: 1, length: 300 };
		spyOn(component.pagination, 'emit');

		component.onPaginationChange(event);

		expect(component.pagination.emit).toHaveBeenCalledWith(event);
	});

	it('#collectDisplayedColumns should collect unique object keys from all records', () => {
		const records = [
			{ a: 1, b: 2, c: 3 },
			{ c: 1, d: 2, e: 3 },
			{ f: 1, g: 2, h: 3 },
			{ f: 1, g: 2, i: 3 },
		];

		const uniqueKeys = component['collectDisplayedColumns'](records);

		expect(uniqueKeys).toEqual(['a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i']);
	});
});
