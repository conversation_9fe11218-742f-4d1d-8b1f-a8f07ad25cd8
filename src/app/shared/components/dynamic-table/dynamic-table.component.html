<div class="iata-dynamic-table__container">
	<table mat-table [dataSource]="dataSource"
		   matSort matSortDisableClear
		   aria-label="Data table"
		   class="iata-dynamic-table">

		@for (columnKey of displayedColumns; track columnKey) {
			<ng-container matColumnDef="{{columnKey}}">
				<th scope="col" mat-header-cell *matHeaderCellDef>{{headers[columnKey] || formatCamelCaseHeader(columnKey)}}</th>
				<td mat-cell *matCellDef="let record">{{record[columnKey]}}</td>
			</ng-container>
		}

		<tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
		<tr mat-row *matRowDef="let record; columns: displayedColumns;" class="iata-dynamic-table__row"></tr>
	</table>
</div>

<mat-paginator
   [pageSizeOptions]="tablePageSizes"
   [length]="totalRecords"
   (page)="onPaginationChange($event)"
></mat-paginator>
