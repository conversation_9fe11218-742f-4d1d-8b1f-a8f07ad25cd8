.orll-piece-in-panel {
	&__title,
	&__total-slac {
		color: var(--iata-grey-600);
		font-size: 20px;
		margin-right: 20px;
	}

	&__add-button {
		color: var(--iata-blue-primary) !important;
		border: 1px solid var(--iata-blue-primary);
		width: 100%;
	}

	.dimensions {
		display: inline-flex;
	}

	.width-100 {
		width: 100%;
	}

	.width-5 {
		width: 7%;
	}

	.lwh {
		width: 34%;
		margin-right: -1px;
	}

	.unit {
		margin-right: 5px;
		font-size: 14px;
		color: var(--iata-black);
	}

	::ng-deep .mat-expansion-panel-header-description {
		justify-content: flex-end;
	}
}
