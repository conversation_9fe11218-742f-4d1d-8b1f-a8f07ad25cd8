import { ComponentFixture, TestBed } from '@angular/core/testing';
import { PieceInPanelComponent } from './piece-in-panel.component';
import { TranslateModule } from '@ngx-translate/core';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';

describe('PieceInPanelComponent', () => {
	let component: PieceInPanelComponent;
	let fixture: ComponentFixture<PieceInPanelComponent>;

	beforeEach(async () => {
		await TestBed.configureTestingModule({
			imports: [PieceInPanelComponent, TranslateModule.forRoot()],
			providers: [provideHttpClient(withInterceptorsFromDi())],
		}).compileComponents();

		fixture = TestBed.createComponent(PieceInPanelComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});
});
