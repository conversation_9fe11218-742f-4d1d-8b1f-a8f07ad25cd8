import { ComponentFixture, TestBed } from '@angular/core/testing';
import { PieceInPanelComponent } from './piece-in-panel.component';
import { TranslateModule } from '@ngx-translate/core';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { By } from '@angular/platform-browser';
import { Piece } from '../../models/piece/piece.model';
import { CodeName } from '@shared/models/code-name.model';
import { of } from 'rxjs';
import { SliCreateRequestService } from '../../services/sli-create-request.service';
import { PieceItemPanelComponent } from '../piece-item-panel/piece-item-panel.component';
import { ChangeDetectorRef, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';

describe('PieceInPanelComponent', () => {
	let component: PieceInPanelComponent;
	let fixture: ComponentFixture<PieceInPanelComponent>;
	let sliCreateRequestServiceSpy: jasmine.SpyObj<SliCreateRequestService>;

	// Mock data
	const mockPackagingTypes: CodeName[] = [
		{ code: 'BOX', name: 'Box' },
		{ code: 'PLT', name: 'Pallet' },
		{ code: 'CTN', name: 'Carton' },
	];

	const mockPiece: Piece = {
		upid: 'TEST123',
		type: 'Piece',
		product: { description: 'Test Product' },
		packagingType: { typeCode: 'BOX', description: 'Box' },
		packagedidentifier: 'ID123',
		nvdForCustoms: true,
		nvdForCarriage: true,
		grossWeight: 50,
		dimensions: { length: 20, width: 15, height: 10 },
		pieceQuantity: 1,
		slac: 0,
		shippingMarks: '',
		textualHandlingInstructions: '',
		containedPieces: [],
		containedItems: [],
	};

	beforeEach(async () => {
		// Create spy for SliCreateRequestService
		sliCreateRequestServiceSpy = jasmine.createSpyObj('SliCreateRequestService', ['getPackingTypes']);
		sliCreateRequestServiceSpy.getPackingTypes.and.returnValue(of(mockPackagingTypes));

		await TestBed.configureTestingModule({
			imports: [PieceInPanelComponent, TranslateModule.forRoot(), NoopAnimationsModule, PieceItemPanelComponent],
			providers: [
				provideHttpClient(withInterceptorsFromDi()),
				{ provide: SliCreateRequestService, useValue: sliCreateRequestServiceSpy },
			],
			schemas: [CUSTOM_ELEMENTS_SCHEMA],
		}).compileComponents();

		fixture = TestBed.createComponent(PieceInPanelComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	it('should initialize with default values and fetch packaging types', () => {
		expect(component.pieceInList).toEqual([]);
		expect(component.packagingTypes).toEqual(mockPackagingTypes);
		expect(component.filteredPackagingTypes).toEqual(mockPackagingTypes);
		expect(component.sliPieceInForm).toBeDefined();
		expect(sliCreateRequestServiceSpy.getPackingTypes).toHaveBeenCalled();
	});

	it('should validate grossWeight field with decimal pattern', () => {
		const grossWeightControl = component.sliPieceInForm.get('grossWeight');

		// Invalid values
		grossWeightControl?.setValue('abc');
		expect(grossWeightControl?.valid).toBeFalsy();

		grossWeightControl?.setValue('10.25'); // More than 1 decimal place
		expect(grossWeightControl?.valid).toBeFalsy();

		// Valid values
		grossWeightControl?.setValue('10');
		expect(grossWeightControl?.valid).toBeTruthy();

		grossWeightControl?.setValue('10.5');
		expect(grossWeightControl?.valid).toBeTruthy();
	});

	it('should validate dimension fields with decimal pattern', () => {
		const dimLengthControl = component.sliPieceInForm.get('dimLength');
		const dimWidthControl = component.sliPieceInForm.get('dimWidth');
		const dimHeightControl = component.sliPieceInForm.get('dimHeight');

		// Test length
		dimLengthControl?.setValue('abc');
		expect(dimLengthControl?.valid).toBeFalsy();
		dimLengthControl?.setValue('10.5');
		expect(dimLengthControl?.valid).toBeTruthy();

		// Test width
		dimWidthControl?.setValue('abc');
		expect(dimWidthControl?.valid).toBeFalsy();
		dimWidthControl?.setValue('15.5');
		expect(dimWidthControl?.valid).toBeTruthy();

		// Test height
		dimHeightControl?.setValue('abc');
		expect(dimHeightControl?.valid).toBeFalsy();
		dimHeightControl?.setValue('20.5');
		expect(dimHeightControl?.valid).toBeTruthy();
	});

	it('should validate quantity field with positive number pattern', () => {
		const quantityControl = component.sliPieceInForm.get('quantity');

		// Invalid values
		quantityControl?.setValue('abc');
		expect(quantityControl?.valid).toBeFalsy();

		quantityControl?.setValue('0'); // Not positive
		expect(quantityControl?.valid).toBeFalsy();

		quantityControl?.setValue('-1'); // Negative
		expect(quantityControl?.valid).toBeFalsy();

		quantityControl?.setValue('1.5'); // Decimal
		expect(quantityControl?.valid).toBeFalsy();

		// Valid values
		quantityControl?.setValue('1');
		expect(quantityControl?.valid).toBeTruthy();

		quantityControl?.setValue('10');
		expect(quantityControl?.valid).toBeTruthy();
	});

	it('should filter packaging types based on search input', () => {
		// Initial state should have all packaging types
		expect(component.filteredPackagingTypes.length).toBe(3);

		// Set search value to filter
		component.sliPieceInForm.get('packagingType')?.setValue('Box');
		expect(component.filteredPackagingTypes.length).toBe(1);
		expect(component.filteredPackagingTypes[0].name).toBe('Box');

		// Empty search should show all
		component.sliPieceInForm.get('packagingType')?.setValue('');
		expect(component.filteredPackagingTypes.length).toBe(3);
	});

	it('should display packaging type name correctly', () => {
		// Existing code
		expect(component.displayPackagingTypeName('BOX')).toBe('Box');

		// Non-existing code
		expect(component.displayPackagingTypeName('UNKNOWN')).toBe('');
	});

	it('should add piece to pieceInList when form is valid', () => {
		// Set form values
		component.sliPieceInForm.setValue({
			productDescription: 'New Product',
			packagingType: 'BOX',
			grossWeight: '15.5',
			dimLength: '30',
			dimWidth: '20',
			dimHeight: '10',
			quantity: '3',
		});

		// Call method
		component.addPieceIn();

		// Check result
		expect(component.pieceInList.length).toBe(1);
		expect(component.pieceInList[0].product.description).toBe('New Product');
		expect(component.pieceInList[0].packagingType.typeCode).toBe('BOX');
		expect(component.pieceInList[0].packagingType.description).toBe('Box');
		expect(component.pieceInList[0].grossWeight).toBe(15.5);
		expect(component.pieceInList[0].dimensions.length).toBe(30);
		expect(component.pieceInList[0].dimensions.width).toBe(20);
		expect(component.pieceInList[0].dimensions.height).toBe(10);
		expect(component.pieceInList[0].pieceQuantity).toBe(3);

		// Form should be reset
		expect(component.sliPieceInForm.value.productDescription).toBeNull();
		expect(component.sliPieceInForm.value.packagingType).toBeNull();
		expect(component.sliPieceInForm.value.grossWeight).toBeNull();
	});

	it('should not add piece when form is invalid', () => {
		// Set invalid form value
		component.sliPieceInForm.setValue({
			productDescription: 'Invalid Product',
			packagingType: 'BOX',
			grossWeight: 'abc', // Invalid weight
			dimLength: '30',
			dimWidth: '20',
			dimHeight: '10',
			quantity: '3',
		});

		// Call method
		component.addPieceIn();

		// Check nothing was added
		expect(component.pieceInList.length).toBe(0);
	});

	it('should delete piece from pieceInList', () => {
		// Add pieces to list
		component.pieceInList = [{ ...mockPiece }, { ...mockPiece, product: { description: 'Second Piece' } }];

		// Delete first piece
		component.deletePiece(0);

		// Check result
		expect(component.pieceInList.length).toBe(1);
		expect(component.pieceInList[0].product.description).toBe('Second Piece');
	});

	it('should render PieceItemPanelComponent for each piece in pieceInList', () => {
		// Add pieces to list
		component.pieceInList = [{ ...mockPiece }, { ...mockPiece }];

		fixture.componentRef.injector.get(ChangeDetectorRef).markForCheck();
		fixture.detectChanges();

		// Find PieceItemPanelComponents
		const pieceItemPanels = fixture.debugElement.queryAll(By.css('orll-piece-item-panel'));
		expect(pieceItemPanels.length).toBe(2);
	});

	it('should pass correct inputs to PieceItemPanelComponent', () => {
		// Add a piece to list
		component.pieceInList = [{ ...mockPiece }];

		fixture.componentRef.injector.get(ChangeDetectorRef).markForCheck();
		fixture.detectChanges();

		// Find PieceItemPanelComponent
		const pieceItemPanel = fixture.debugElement.query(By.css('orll-piece-item-panel'));
		const pieceItemPanelInstance = pieceItemPanel.componentInstance;

		// Check inputs
		expect(pieceItemPanelInstance.containedPiece).toEqual(mockPiece);
		expect(pieceItemPanelInstance.pieceIndex).toBe(0);
	});

	it('should handle deletePiece event from PieceItemPanelComponent', () => {
		// Spy on component method
		spyOn(component, 'deletePiece');

		// Add a piece to list
		component.pieceInList = [{ ...mockPiece }];

		fixture.componentRef.injector.get(ChangeDetectorRef).markForCheck();
		fixture.detectChanges();

		// Find PieceItemPanelComponent
		const pieceItemPanel = fixture.debugElement.query(By.css('orll-piece-item-panel'));

		// Trigger deletePiece event
		pieceItemPanel.triggerEventHandler('deletePiece', null);

		// Check if component method was called
		expect(component.deletePiece).toHaveBeenCalledWith(0);
	});
});
