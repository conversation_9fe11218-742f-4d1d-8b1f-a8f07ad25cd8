import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatListModule } from '@angular/material/list';
import { MatDividerModule } from '@angular/material/divider';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { DestroyRefComponent } from '@shared/components/destroy-observable/destroy-ref.component';
import { CommonModule } from '@angular/common';
import { Piece } from '../../models/piece/piece.model';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { CodeName } from '@shared/models/code-name.model';
import { SliCreateRequestService } from '../../services/sli-create-request.service';
import { startWith } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { PieceItemPanelComponent } from '../piece-item-panel/piece-item-panel.component';

const REGX_NUMBER_1_DECIMAL = '^\\d+(\\.\\d{1})?$';
const REGX_POSITIVE_NUMBER = '^[1-9]\\d*$';

@Component({
	selector: 'orll-piece-in-panel',
	templateUrl: './piece-in-panel.component.html',
	styleUrl: './piece-in-panel.component.scss',
	changeDetection: ChangeDetectionStrategy.OnPush,
	imports: [
		MatIconModule,
		MatButtonModule,
		MatExpansionModule,
		MatInputModule,
		MatListModule,
		MatDividerModule,
		MatFormFieldModule,
		ReactiveFormsModule,
		FormsModule,
		CommonModule,
		TranslateModule,
		MatAutocompleteModule,
		PieceItemPanelComponent,
	],
})
export class PieceInPanelComponent extends DestroyRefComponent {
	@Input() pieceInList: Piece[] = [];

	packagingTypes: CodeName[] = [];
	filteredPackagingTypes: CodeName[] = [];

	sliPieceInForm: FormGroup = new FormGroup({
		productDescription: new FormControl<string>(''),
		packagingType: new FormControl<string>(''),
		grossWeight: new FormControl<string>('', [Validators.pattern(REGX_NUMBER_1_DECIMAL)]),
		dimLength: new FormControl<string>('', [Validators.pattern(REGX_NUMBER_1_DECIMAL)]),
		dimWidth: new FormControl<string>('', [Validators.pattern(REGX_NUMBER_1_DECIMAL)]),
		dimHeight: new FormControl<string>('', [Validators.pattern(REGX_NUMBER_1_DECIMAL)]),
		quantity: new FormControl<string>('', [Validators.pattern(REGX_POSITIVE_NUMBER)]),
	});

	constructor(private readonly sliCreateRequestService: SliCreateRequestService) {
		super();
	}

	ngOnInit(): void {
		this.initRefData();
		this.setupAutocomplete();
	}

	private initRefData(): void {
		this.sliCreateRequestService.getPackingTypes().subscribe((packagingTypes: CodeName[]) => {
			this.packagingTypes = packagingTypes;
			this.filteredPackagingTypes = packagingTypes;
		});
	}

	private setupAutocomplete(): void {
		this.sliPieceInForm
			.get('packagingType')
			?.valueChanges.pipe(startWith(''), takeUntilDestroyed(this.destroyRef))
			.subscribe((search) => {
				this.filteredPackagingTypes = this.packagingTypes.filter((packagingTypes) =>
					packagingTypes.name.toLowerCase().includes(search?.toLowerCase().trim() ?? '')
				);
			});
	}

	displayPackagingTypeName = (code: string): string => {
		const type = this.packagingTypes.find((item) => item.code === code);
		return type?.name ?? '';
	};

	addPieceIn(): void {
		if (this.sliPieceInForm.invalid) {
			return;
		}

		let packagingType = { typeCode: '', description: '' };
		if (this.sliPieceInForm.value.packagingType) {
			packagingType = this.packagingTypes
				.filter((item) => item.code === this.sliPieceInForm.value.packagingType)
				.map((item) => {
					return { typeCode: item.code, description: item.name };
				})[0];
		}

		this.pieceInList.push({
			product: {
				description: this.sliPieceInForm.value.productDescription ?? '',
			},
			packagingType,
			packagedidentifier: '',
			grossWeight: Number(this.sliPieceInForm.value.grossWeight ?? ''),
			pieceQuantity: Number(this.sliPieceInForm.value.quantity ?? ''),
			nvdForCustoms: true,
			nvdForCarriage: true,
			upid: '',
			type: 'Piece',
			dimensions: {
				length: Number(this.sliPieceInForm.value.dimLength ?? ''),
				width: Number(this.sliPieceInForm.value.dimWidth ?? ''),
				height: Number(this.sliPieceInForm.value.dimHeight ?? ''),
			},
			slac: 0,
			shippingMarks: '',
			textualHandlingInstructions: '',
			containedPieces: [],
			containedItems: [],
		});

		this.sliPieceInForm.reset();
	}

	deletePiece(pieceIndex: number): void {
		this.pieceInList.splice(pieceIndex, 1);
	}
}
