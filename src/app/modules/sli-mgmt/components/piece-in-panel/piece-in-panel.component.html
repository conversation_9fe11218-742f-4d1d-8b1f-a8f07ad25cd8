<div class="orll-piece-in-panel">
	<mat-expansion-panel class="orll-piece-in-panel__panel" expanded>
		<mat-expansion-panel-header>
			<mat-panel-title>
				<div class="mat-display-2 orll-piece-in-panel__title">
					{{'sli.piece.contained.title' | translate}}
				</div>
			</mat-panel-title>
			<mat-panel-description>
				<div class="orll-piece-in-panel__total-slac">
					<span>{{'sli.piece.slac.total' | translate}}: {{pieceInList.length}}</span>
				</div>
			</mat-panel-description>
		</mat-expansion-panel-header>

		<form [formGroup]="sliPieceInForm">
			<div class="row">
				<div class="col-3">
					<mat-form-field appearance="outline" class="width-100">
						<mat-label>{{'sli.piece.productDescription' | translate}}</mat-label>
						<input matInput formControlName="productDescription" placeholder="{{'sli.piece.productDescription' | translate}}">
					</mat-form-field>
				</div>
				<div class="col-2">
					<mat-form-field appearance="outline" class="width-100">
						<mat-icon matSuffix class="autocomplete-arrow">keyboard_arrow_down</mat-icon>
						<mat-label>{{'sli.piece.packagingType' | translate}}</mat-label>
						<input type="text" matInput
							formControlName="packagingType"
							[matAutocomplete]="autoPackagingType">
						<mat-autocomplete #autoPackagingType="matAutocomplete" [displayWith]="displayPackagingTypeName">
							@for (packagingTypes of filteredPackagingTypes; track packagingTypes) {
								<mat-option [value]="packagingTypes.code">{{packagingTypes.name}}</mat-option>
							}
						</mat-autocomplete>
					</mat-form-field>
				</div>
				<div class="col-1">
					<mat-form-field appearance="outline" class="width-100">
						<mat-label>{{'sli.piece.grossWeight' | translate}}</mat-label>
						<input matInput formControlName="grossWeight" placeholder="{{'sli.piece.grossWeight' | translate}}">
						<span matSuffix class="unit">KG</span>
						@if (sliPieceInForm.get('grossWeight')?.hasError('pattern')) {
							<mat-error>{{'sli.mgmt.pieceList.pattern.decimalNumber1' | translate}}</mat-error>
						}
					</mat-form-field>
				</div>
				<div class="col-3 dimensions">
					<div class="lwh">
						<mat-form-field appearance="outline" class="width-100">
							<mat-label>{{'sli.piece.dimensions' | translate}}</mat-label>
							<input matInput formControlName="dimLength" placeholder="{{'sli.mgmt.pieceList.dimLength' | translate}}">
							<span matSuffix class="unit">CM</span>
							@if (sliPieceInForm.get('dimLength')?.hasError('pattern')) {
								<mat-error>{{'sli.mgmt.pieceList.pattern.decimalNumber1' | translate}}</mat-error>
							}
						</mat-form-field>
					</div>
					<div class="lwh">
						<mat-form-field appearance="outline" class="width-100">
							<input matInput formControlName="dimWidth" placeholder="{{'sli.mgmt.pieceList.dimWidth' | translate}}">
							<span matSuffix class="unit">CM</span>
							@if (sliPieceInForm.get('dimWidth')?.hasError('pattern')) {
								<mat-error>{{'sli.mgmt.pieceList.pattern.decimalNumber1' | translate}}</mat-error>
							}
						</mat-form-field>
					</div>
					<div class="lwh">
						<mat-form-field appearance="outline" class="width-100">
							<input matInput formControlName="dimHeight" placeholder="{{'sli.mgmt.pieceList.dimHeight' | translate}}">
							<span matSuffix class="unit">CM</span>
							@if (sliPieceInForm.get('dimHeight')?.hasError('pattern')) {
								<mat-error>{{'sli.mgmt.pieceList.pattern.decimalNumber1' | translate}}</mat-error>
							}
						</mat-form-field>
					</div>
				</div>
				<div class="col-1">
					<mat-form-field appearance="outline" class="width-100">
						<mat-label>{{'sli.piece.item.quantity' | translate}}</mat-label>
						<input matInput formControlName="quantity" placeholder="{{'sli.piece.item.quantity' | translate}}">
						@if (sliPieceInForm.get('quantity')?.hasError('pattern')) {
							<mat-error>{{'sli.mgmt.pieceList.pattern.positiveNumber' | translate}}</mat-error>
						}
					</mat-form-field>
				</div>

				<div class="width-5">
					<button mat-raised-button (click)="addPieceIn()" class="orll-piece-in-panel__add-button">
						<mat-icon>add</mat-icon>
						{{'sli.piece.add.pieceIn' | translate}}
					</button>
				</div>
			</div>
		</form>

		<mat-accordion multi>
			@for (piece of pieceInList; track piece; let pieceIndex = $index) {
				<orll-piece-item-panel [containedPiece]="piece" [pieceIndex]="pieceIndex" (deletePiece)="deletePiece(pieceIndex)"></orll-piece-item-panel>
			}
		</mat-accordion>
	</mat-expansion-panel>
</div>
