import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { TranslateModule } from '@ngx-translate/core';
import { DestroyRefComponent } from '@shared/components/destroy-observable/destroy-ref.component';
import { CodeName } from '@shared/models/code-name.model';
import { startWith } from 'rxjs';
import { SliCreateRequestService } from '../../services/sli-create-request.service';
import { DropDownType } from '@shared/models/dropdown-type.model';

const REGX_NUMBER_1_DECIMAL = '^\\d+(\\.\\d{1})?$';

@Component({
	selector: 'orll-sli-piece-form',
	templateUrl: './sli-piece-form.component.html',
	styleUrl: './sli-piece-form.component.scss',
	changeDetection: ChangeDetectionStrategy.OnPush,
	imports: [
		MatInputModule,
		MatSelectModule,
		MatIconModule,
		TranslateModule,
		ReactiveFormsModule,
		FormsModule,
		MatFormFieldModule,
		CommonModule,
		MatAutocompleteModule,
	],
})
export class SliPieceFormComponent extends DestroyRefComponent implements OnInit {
	packagingTypes: CodeName[] = [];
	filteredPackagingTypes: CodeName[] = [];
	nvdForCustoms: string[] = [DropDownType.NCV, DropDownType.YES];
	nvdForCarriage: string[] = [DropDownType.NVP, DropDownType.YES];

	sliPieceForm: FormGroup = new FormGroup({
		productDescription: new FormControl<string>('', [Validators.required]),
		hsCommodityDescription: new FormControl<string>(''),
		packagingType: new FormControl<string>('', [Validators.required]),
		packagedIdentifier: new FormControl<string>(''),
		grossWeight: new FormControl<string>('', [Validators.required, Validators.pattern(REGX_NUMBER_1_DECIMAL)]),
		dimLength: new FormControl<string>('', [Validators.required, Validators.pattern(REGX_NUMBER_1_DECIMAL)]),
		dimWidth: new FormControl<string>('', [Validators.required, Validators.pattern(REGX_NUMBER_1_DECIMAL)]),
		dimHeight: new FormControl<string>('', [Validators.required, Validators.pattern(REGX_NUMBER_1_DECIMAL)]),
		nvdForCustoms: new FormControl<string>('NCV'),
		nvdForCarriage: new FormControl<string>('NVP'),
		upid: new FormControl<string>(''),
		shippingMarks: new FormControl<string>(''),
		textualHandlingInstructions: new FormControl<string>(''),
		pieceQuantity: new FormControl<string>('', [Validators.required]),
	});

	constructor(private readonly sliCreateRequestService: SliCreateRequestService) {
		super();
	}

	ngOnInit(): void {
		this.initRefData();
		this.setupAutocomplete();
	}

	private initRefData(): void {
		this.sliCreateRequestService.getPackingTypes().subscribe((packagingTypes: CodeName[]) => {
			this.packagingTypes = packagingTypes;
			this.filteredPackagingTypes = packagingTypes;
		});
	}

	private setupAutocomplete(): void {
		this.sliPieceForm
			.get('packagingType')
			?.valueChanges.pipe(startWith(''), takeUntilDestroyed(this.destroyRef))
			.subscribe((search) => {
				this.filteredPackagingTypes = this.packagingTypes.filter((packagingTypes) =>
					packagingTypes.name.toLowerCase().includes(search?.toLowerCase().trim() ?? '')
				);
			});
	}

	displayPackagingTypeName = (code: string): string => {
		const type = this.packagingTypes.find((item) => item.code === code);
		return type?.name ?? '';
	};
}
