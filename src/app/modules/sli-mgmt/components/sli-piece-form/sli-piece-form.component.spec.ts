import { ComponentFixture, TestBed } from '@angular/core/testing';
import { SliPieceFormComponent } from './sli-piece-form.component';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { TranslateModule } from '@ngx-translate/core';
import { ReactiveFormsModule } from '@angular/forms';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { SliCreateRequestService } from '../../services/sli-create-request.service';
import { of } from 'rxjs';
import { CodeName } from '@shared/models/code-name.model';
import { DropDownType } from '@shared/models/dropdown-type.model';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';

describe('SliPieceFormComponent', () => {
	let component: SliPieceFormComponent;
	let fixture: ComponentFixture<SliPieceFormComponent>;
	let sliCreateRequestServiceSpy: jasmine.SpyObj<SliCreateRequestService>;
	const mockPackagingTypes: CodeName[] = [
		{ code: 'BOX', name: 'Box' },
		{ code: 'PALLET', name: 'Pallet' },
		{ code: 'CRATE', name: 'Crate' },
	];

	beforeEach(async () => {
		sliCreateRequestServiceSpy = jasmine.createSpyObj('SliCreateRequestService', ['getPackingTypes']);
		sliCreateRequestServiceSpy.getPackingTypes.and.returnValue(of(mockPackagingTypes));

		await TestBed.configureTestingModule({
			imports: [
				SliPieceFormComponent,
				TranslateModule.forRoot(),
				ReactiveFormsModule,
				NoopAnimationsModule,
				MatAutocompleteModule,
				MatFormFieldModule,
				MatInputModule,
				MatSelectModule,
			],
			providers: [
				provideHttpClient(withInterceptorsFromDi()),
				{ provide: SliCreateRequestService, useValue: sliCreateRequestServiceSpy },
			],
		}).compileComponents();

		fixture = TestBed.createComponent(SliPieceFormComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	it('should initialize the form with default values', () => {
		// Assert form controls exist and have default values
		expect(component.sliPieceForm.get('productDescription')).toBeTruthy();
		expect(component.sliPieceForm.get('packagingType')).toBeTruthy();
		expect(component.sliPieceForm.get('grossWeight')).toBeTruthy();
		expect(component.sliPieceForm.get('dimLength')).toBeTruthy();
		expect(component.sliPieceForm.get('dimWidth')).toBeTruthy();
		expect(component.sliPieceForm.get('dimHeight')).toBeTruthy();

		// Check default values for dropdown fields
		expect(component.sliPieceForm.get('nvdForCustoms')?.value).toBe('NCV');
		expect(component.sliPieceForm.get('nvdForCarriage')?.value).toBe('NVP');
	});

	it('should load packaging types on init', () => {
		// Assert the service was called
		expect(sliCreateRequestServiceSpy.getPackingTypes).toHaveBeenCalled();

		// Assert the data was stored in the component
		expect(component.packagingTypes).toEqual(mockPackagingTypes);
		expect(component.filteredPackagingTypes).toEqual(mockPackagingTypes);
	});

	it('should validate required fields', () => {
		// Initially form should be invalid
		expect(component.sliPieceForm.valid).toBeFalsy();

		// Set required values
		component.sliPieceForm.patchValue({
			productDescription: 'Test Product',
			packagingType: 'BOX',
			grossWeight: '10.5',
			dimLength: '20.0',
			dimWidth: '15.0',
			dimHeight: '10.0',
			pieceQuantity: '1',
		});

		// Form should now be valid
		expect(component.sliPieceForm.valid).toBeTruthy();
	});

	it('should validate numeric fields with decimal pattern', () => {
		const grossWeightControl = component.sliPieceForm.get('grossWeight');

		// Invalid values
		grossWeightControl?.setValue('abc');
		expect(grossWeightControl?.valid).toBeFalsy();

		grossWeightControl?.setValue('10.25'); // More than 1 decimal place
		expect(grossWeightControl?.valid).toBeFalsy();

		// Valid values
		grossWeightControl?.setValue('10');
		expect(grossWeightControl?.valid).toBeTruthy();

		grossWeightControl?.setValue('10.5');
		expect(grossWeightControl?.valid).toBeTruthy();
	});

	it('should filter packaging types based on search input', () => {
		// Simulate user typing in the autocomplete
		component.sliPieceForm.get('packagingType')?.setValue('Box');
		fixture.detectChanges();

		// Should filter to only show 'Box'
		expect(component.filteredPackagingTypes.length).toBe(1);
		expect(component.filteredPackagingTypes[0].code).toBe('BOX');

		// Try another search
		component.sliPieceForm.get('packagingType')?.setValue('Pal');
		fixture.detectChanges();

		// Should filter to only show 'Pallet'
		expect(component.filteredPackagingTypes.length).toBe(1);
		expect(component.filteredPackagingTypes[0].code).toBe('PALLET');

		// Empty search should show all
		component.sliPieceForm.get('packagingType')?.setValue('');
		fixture.detectChanges();

		expect(component.filteredPackagingTypes.length).toBe(mockPackagingTypes.length);
	});

	it('should display packaging type name correctly', () => {
		// Test with existing code
		expect(component.displayPackagingTypeName('BOX')).toBe('Box');

		// Test with non-existent code
		expect(component.displayPackagingTypeName('NONEXISTENT')).toBe('');
	});

	it('should initialize dropdown values correctly', () => {
		expect(component.nvdForCustoms).toEqual([DropDownType.NCV, DropDownType.YES]);
		expect(component.nvdForCarriage).toEqual([DropDownType.NVP, DropDownType.YES]);
	});
});
