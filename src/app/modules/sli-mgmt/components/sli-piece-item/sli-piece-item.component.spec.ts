import { ComponentFixture, TestBed } from '@angular/core/testing';
import { SliPieceItemComponent } from './sli-piece-item.component';
import { TranslateModule } from '@ngx-translate/core';

describe('SliPieceItemComponent', () => {
	let component: SliPieceItemComponent;
	let fixture: ComponentFixture<SliPieceItemComponent>;

	beforeEach(async () => {
		await TestBed.configureTestingModule({
			imports: [SliPieceItemComponent, TranslateModule.forRoot()],
		}).compileComponents();

		fixture = TestBed.createComponent(SliPieceItemComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});
});
