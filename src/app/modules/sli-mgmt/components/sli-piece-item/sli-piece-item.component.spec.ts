import { ComponentFixture, TestBed } from '@angular/core/testing';
import { SliPieceItemComponent } from './sli-piece-item.component';
import { TranslateModule } from '@ngx-translate/core';
import { FormsModule } from '@angular/forms';
import { MatRadioModule } from '@angular/material/radio';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { By } from '@angular/platform-browser';
import { PieceItemPanelComponent } from '../piece-item-panel/piece-item-panel.component';
import { PieceInPanelComponent } from '../piece-in-panel/piece-in-panel.component';
import { Piece } from '../../models/piece/piece.model';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { ChangeDetectorRef } from '@angular/core';

describe('SliPieceItemComponent', () => {
	let component: SliPieceItemComponent;
	let fixture: ComponentFixture<SliPieceItemComponent>;

	// Mock data
	const mockPiece: Piece = {
		upid: 'TEST123',
		type: 'Piece',
		product: { description: 'Test Product' },
		packagingType: { typeCode: 'BOX', description: 'Box' },
		packagedidentifier: 'ID123',
		nvdForCustoms: true,
		nvdForCarriage: true,
		grossWeight: 50,
		dimensions: { length: 20, width: 15, height: 10 },
		pieceQuantity: 1,
		slac: 0,
		shippingMarks: '',
		textualHandlingInstructions: '',
		containedPieces: [],
		containedItems: [],
	};

	beforeEach(async () => {
		await TestBed.configureTestingModule({
			imports: [
				SliPieceItemComponent,
				TranslateModule.forRoot(),
				FormsModule,
				MatRadioModule,
				NoopAnimationsModule,
				PieceItemPanelComponent,
				PieceInPanelComponent,
			],
			providers: [provideHttpClient(withInterceptorsFromDi())],
		}).compileComponents();

		fixture = TestBed.createComponent(SliPieceItemComponent);
		component = fixture.componentInstance;
	});

	describe('Component Initialization', () => {
		it('should create', () => {
			expect(component).toBeTruthy();
		});

		it('should initialize with default values', () => {
			expect(component.pieceType).toBe('');
			expect(component.piece).toBeNull();
			expect(component.isContained).toBe('no');
		});

		it('should accept pieceType input', () => {
			component.pieceType = 'general';
			expect(component.pieceType).toBe('general');
		});
	});

	describe('Template Rendering - General Piece Type', () => {
		beforeEach(() => {
			component.pieceType = 'general';
			fixture.detectChanges();
		});

		it('should display header section when pieceType is general', () => {
			const header = fixture.debugElement.query(By.css('.orll-piece-add-item__header'));
			expect(header).toBeTruthy();
		});

		it('should display title when pieceType is general', () => {
			const title = fixture.debugElement.query(By.css('.orll-piece-add-item__title'));
			expect(title).toBeTruthy();
		});

		it('should display radio buttons when pieceType is general', () => {
			const radioGroup = fixture.debugElement.query(By.css('mat-radio-group'));
			expect(radioGroup).toBeTruthy();

			const radioButtons = fixture.debugElement.queryAll(By.css('mat-radio-button'));
			expect(radioButtons.length).toBe(2);
		});

		it('should have correct radio button values', () => {
			const radioButtons = fixture.debugElement.queryAll(By.css('mat-radio-button'));
			expect(radioButtons[0].nativeElement.getAttribute('ng-reflect-value')).toBe('yes');
			expect(radioButtons[1].nativeElement.getAttribute('ng-reflect-value')).toBe('no');
		});

		it('should have "no" radio button selected by default', () => {
			const radioGroup = fixture.debugElement.query(By.css('mat-radio-group'));
			expect(radioGroup.nativeElement.getAttribute('ng-reflect-model')).toBe('no');
		});
	});

	describe('Template Rendering - Non-General Piece Type', () => {
		beforeEach(() => {
			component.pieceType = 'dg';
			fixture.detectChanges();
		});

		it('should not display header section when pieceType is not general', () => {
			const header = fixture.debugElement.query(By.css('.orll-piece-add-item__header'));
			expect(header).toBeFalsy();
		});

		it('should not display radio buttons when pieceType is not general', () => {
			const radioGroup = fixture.debugElement.query(By.css('mat-radio-group'));
			expect(radioGroup).toBeFalsy();
		});
	});

	describe('Conditional Panel Rendering', () => {
		beforeEach(() => {
			component.pieceType = 'general';
		});

		it('should display PieceItemPanelComponent when isContained is "no"', () => {
			component.isContained = 'no';
			fixture.detectChanges();

			const pieceItemPanel = fixture.debugElement.query(By.css('orll-piece-item-panel'));
			const pieceInPanel = fixture.debugElement.query(By.css('orll-piece-in-panel'));

			expect(pieceItemPanel).toBeTruthy();
			expect(pieceInPanel).toBeFalsy();
		});

		it('should display PieceInPanelComponent when isContained is "yes"', () => {
			component.isContained = 'yes';
			fixture.detectChanges();

			const pieceItemPanel = fixture.debugElement.query(By.css('orll-piece-item-panel'));
			const pieceInPanel = fixture.debugElement.query(By.css('orll-piece-in-panel'));

			expect(pieceItemPanel).toBeFalsy();
			expect(pieceInPanel).toBeTruthy();
		});

		it('should display PieceInPanelComponent when isContained is any value other than "no"', () => {
			component.isContained = 'maybe';
			fixture.detectChanges();

			const pieceItemPanel = fixture.debugElement.query(By.css('orll-piece-item-panel'));
			const pieceInPanel = fixture.debugElement.query(By.css('orll-piece-in-panel'));

			expect(pieceItemPanel).toBeFalsy();
			expect(pieceInPanel).toBeTruthy();
		});
	});

	describe('Radio Button Interaction', () => {
		beforeEach(() => {
			component.pieceType = 'general';
			fixture.detectChanges();
		});

		it('should update isContained when "yes" radio button is clicked', () => {
			const radioGroup = fixture.debugElement.query(By.css('mat-radio-group'));

			// Simulate radio group change event
			radioGroup.triggerEventHandler('ngModelChange', 'yes');
			fixture.detectChanges();

			expect(component.isContained).toBe('yes');
		});

		it('should update isContained when "no" radio button is clicked', () => {
			// First set to 'yes'
			component.isContained = 'yes';
			fixture.detectChanges();

			const radioGroup = fixture.debugElement.query(By.css('mat-radio-group'));

			// Simulate radio group change event
			radioGroup.triggerEventHandler('ngModelChange', 'no');
			fixture.detectChanges();

			expect(component.isContained).toBe('no');
		});

		it('should switch between panels when radio button selection changes', () => {
			// Start with 'no' (default)
			expect(component.isContained).toBe('no');

			// Should show PieceItemPanel
			let pieceItemPanel = fixture.debugElement.query(By.css('orll-piece-item-panel'));
			let pieceInPanel = fixture.debugElement.query(By.css('orll-piece-in-panel'));
			expect(pieceItemPanel).toBeTruthy();
			expect(pieceInPanel).toBeFalsy();

			// Change to 'yes' - need to trigger change detection for OnPush component
			component.isContained = 'yes';
			fixture.componentRef.injector.get(ChangeDetectorRef).markForCheck();
			fixture.detectChanges();

			// Should now show PieceInPanel
			pieceItemPanel = fixture.debugElement.query(By.css('orll-piece-item-panel'));
			pieceInPanel = fixture.debugElement.query(By.css('orll-piece-in-panel'));
			expect(pieceItemPanel).toBeFalsy();
			expect(pieceInPanel).toBeTruthy();
		});
	});

	describe('Data Binding to Child Components', () => {
		beforeEach(() => {
			component.piece = mockPiece;
			fixture.detectChanges();
		});

		it('should pass empty array to PieceItemPanelComponent when piece is null', () => {
			component.piece = null;
			component.isContained = 'no';
			fixture.detectChanges();

			const pieceItemPanel = fixture.debugElement.query(By.css('orll-piece-item-panel'));
			expect(pieceItemPanel).toBeTruthy();
			expect(pieceItemPanel.componentInstance.pieceItemList).toEqual([]);
		});

		it('should pass containedItems to PieceItemPanelComponent when piece exists', () => {
			component.isContained = 'no';
			fixture.detectChanges();

			const pieceItemPanel = fixture.debugElement.query(By.css('orll-piece-item-panel'));
			expect(pieceItemPanel).toBeTruthy();
			expect(pieceItemPanel.componentInstance.pieceItemList).toBe(mockPiece.containedItems);
		});

		it('should pass empty array to PieceInPanelComponent when piece is null', () => {
			component.piece = null;
			component.isContained = 'yes';
			fixture.componentRef.injector.get(ChangeDetectorRef).markForCheck();
			fixture.detectChanges();

			const pieceInPanel = fixture.debugElement.query(By.css('orll-piece-in-panel'));
			expect(pieceInPanel).toBeTruthy();
			expect(pieceInPanel.componentInstance.pieceInList).toEqual([]);
		});

		it('should pass containedPieces to PieceInPanelComponent when piece exists', () => {
			component.isContained = 'yes';
			fixture.componentRef.injector.get(ChangeDetectorRef).markForCheck();
			fixture.detectChanges();

			const pieceInPanel = fixture.debugElement.query(By.css('orll-piece-in-panel'));
			expect(pieceInPanel).toBeTruthy();
			expect(pieceInPanel.componentInstance.pieceInList).toBe(mockPiece.containedPieces);
		});
	});

	describe('Component Integration', () => {
		it('should render body section regardless of pieceType', () => {
			// Test with general type
			component.pieceType = 'general';
			fixture.detectChanges();

			let body = fixture.debugElement.query(By.css('.orll-piece-add-item__body'));
			expect(body).toBeTruthy();

			// Test with non-general type
			component.pieceType = 'dg';
			fixture.detectChanges();

			body = fixture.debugElement.query(By.css('.orll-piece-add-item__body'));
			expect(body).toBeTruthy();
		});

		it('should maintain component state when pieceType changes', () => {
			component.pieceType = 'general';
			component.isContained = 'yes';
			component.piece = mockPiece;
			fixture.detectChanges();

			// Change pieceType
			component.pieceType = 'dg';
			fixture.detectChanges();

			// State should be preserved
			expect(component.isContained).toBe('yes');
			expect(component.piece).toBe(mockPiece);
		});
	});
});
