import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, Input } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatRadioModule } from '@angular/material/radio';
import { TranslateModule } from '@ngx-translate/core';
import { PieceItemPanelComponent } from '../piece-item-panel/piece-item-panel.component';
import { PieceInPanelComponent } from '../piece-in-panel/piece-in-panel.component';
import { Piece } from '../../models/piece/piece.model';

@Component({
	selector: 'orll-sli-piece-item',
	templateUrl: './sli-piece-item.component.html',
	styleUrl: './sli-piece-item.component.scss',
	changeDetection: ChangeDetectionStrategy.OnPush,
	imports: [MatRadioModule, FormsModule, TranslateModule, PieceItemPanelComponent, PieceInPanelComponent],
	schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class SliPieceItemComponent {
	@Input() pieceType = '';

	piece: Piece | null = null;
	isContained = 'no';
}
