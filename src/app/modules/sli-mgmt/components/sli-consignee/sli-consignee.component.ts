import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnChanges, SimpleChanges } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { DestroyRefComponent } from '@shared/components/destroy-observable/destroy-ref.component';
import { ShipmentParty } from '../../models/shipment-party.model';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatDialog } from '@angular/material/dialog';
import { SelectOrgDialogComponent } from '@shared/components/select-org-dialog/select-org-dialog.component';
import { Person } from '@shared/models/person.model';
import { OrgType } from '@shared/models/org-type.model';

@Component({
	selector: 'orll-sli-consignee',
	templateUrl: './sli-consignee.component.html',
	styleUrl: './sli-consignee.component.scss',
	changeDetection: ChangeDetectionStrategy.OnPush,
	imports: [MatIconModule, MatButtonModule, TranslateModule, MatExpansionModule],
})
export class SliConsigneeComponent extends DestroyRefComponent implements OnChanges {
	@Input() title?: '';
	@Input() shipmentPartyInput: ShipmentParty | null = null;
	shipmentParty: ShipmentParty | null = null;

	constructor(
		private readonly dialog: MatDialog,
		private readonly cdr: ChangeDetectorRef
	) {
		super();
	}

	ngOnChanges(changes: SimpleChanges): void {
		if (changes['shipmentPartyInput']) {
			this.shipmentParty = this.shipmentPartyInput;
		}
	}

	getOrgList(): void {
		const dialogRef = this.dialog.open(SelectOrgDialogComponent, {
			width: '400px',
			data: {
				orgType: '',
			},
		});

		dialogRef.afterClosed().subscribe((result) => {
			if (!result) return;

			this.shipmentParty = {
				...this.shipmentParty,
				companyName: result.companyName,
				contactName: result.persons.find((person: Person) => person.contactRole === OrgType.CUSTOMER_CONTACT)?.contactName ?? '',
				countryCode: result.countryCode,
				regionCode: result.regionCode,
				cityCode: result.cityCode,
				postalCode: result.textualPostCode,
				locationName: result.locationName,
				phoneNumber: result.persons.find((person: Person) => person.contactRole === OrgType.CUSTOMER_CONTACT)?.phoneNumber ?? '',
				emailAddress: result.persons.find((person: Person) => person.contactRole === OrgType.CUSTOMER_CONTACT)?.emailAddress ?? '',
				companyType: OrgType.CONSIGNEE,
			};

			this.cdr.markForCheck();
		});
	}

	getFormData(): ShipmentParty | null {
		return this.shipmentParty;
	}
}
