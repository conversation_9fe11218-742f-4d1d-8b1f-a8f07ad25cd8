import { ComponentFixture, TestBed } from '@angular/core/testing';
import { PieceItemPanelComponent } from './piece-item-panel.component';
import { TranslateModule } from '@ngx-translate/core';
import { ReactiveFormsModule } from '@angular/forms';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatListModule } from '@angular/material/list';
import { MatDividerModule } from '@angular/material/divider';
import { MatTooltipModule } from '@angular/material/tooltip';
import { Piece } from '../../models/piece/piece.model';
import { PieceItem } from '../../models/piece/piece-item.model';
import { By } from '@angular/platform-browser';

describe('PieceItemPanelComponent', () => {
	let component: PieceItemPanelComponent;
	let fixture: ComponentFixture<PieceItemPanelComponent>;

	// Mock data
	const mockPieceItem: PieceItem = {
		product: { description: 'Test Item' },
		weight: 10.5,
		itemQuantity: 5,
	};

	const mockPiece: Piece = {
		upid: 'TEST123',
		type: 'TEST',
		product: { description: 'Test Piece' },
		packagingType: { typeCode: 'BOX', description: 'Box' },
		packagedidentifier: 'ID123',
		nvdForCustoms: false,
		nvdForCarriage: false,
		grossWeight: 50,
		dimensions: { length: 20, width: 15, height: 10 },
		pieceQuantity: 1,
		slac: 0,
		shippingMarks: 'TEST',
		textualHandlingInstructions: 'Handle with care',
		containedPieces: [],
		containedItems: [],
	};

	beforeEach(async () => {
		await TestBed.configureTestingModule({
			imports: [
				PieceItemPanelComponent,
				TranslateModule.forRoot(),
				ReactiveFormsModule,
				NoopAnimationsModule,
				MatExpansionModule,
				MatFormFieldModule,
				MatInputModule,
				MatButtonModule,
				MatIconModule,
				MatListModule,
				MatDividerModule,
				MatTooltipModule,
			],
		}).compileComponents();

		fixture = TestBed.createComponent(PieceItemPanelComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	it('should initialize with default values', () => {
		expect(component.pieceIndex).toBe(0);
		expect(component.containedPiece).toBeNull();
		expect(component.pieceItemList).toEqual([]);
		expect(component.sliPieceItemForm).toBeDefined();
		expect(component.sliPieceItemForm.get('description')).toBeDefined();
		expect(component.sliPieceItemForm.get('weight')).toBeDefined();
		expect(component.sliPieceItemForm.get('quantity')).toBeDefined();
	});

	it('should validate weight field with decimal pattern', () => {
		const weightControl = component.sliPieceItemForm.get('weight');

		// Invalid values
		weightControl?.setValue('abc');
		expect(weightControl?.valid).toBeFalsy();

		weightControl?.setValue('10.25'); // More than 1 decimal place
		expect(weightControl?.valid).toBeFalsy();

		// Valid values
		weightControl?.setValue('10');
		expect(weightControl?.valid).toBeTruthy();

		weightControl?.setValue('10.5');
		expect(weightControl?.valid).toBeTruthy();
	});

	it('should validate quantity field with positive number pattern', () => {
		const quantityControl = component.sliPieceItemForm.get('quantity');

		// Invalid values
		quantityControl?.setValue('abc');
		expect(quantityControl?.valid).toBeFalsy();

		quantityControl?.setValue('0'); // Not positive
		expect(quantityControl?.valid).toBeFalsy();

		quantityControl?.setValue('-1'); // Negative
		expect(quantityControl?.valid).toBeFalsy();

		quantityControl?.setValue('1.5'); // Decimal
		expect(quantityControl?.valid).toBeFalsy();

		// Valid values
		quantityControl?.setValue('1');
		expect(quantityControl?.valid).toBeTruthy();

		quantityControl?.setValue('10');
		expect(quantityControl?.valid).toBeTruthy();
	});

	it('should add piece item to pieceItemList when containedPiece is null', () => {
		// Set form values
		component.sliPieceItemForm.setValue({
			description: 'New Item',
			weight: '15.5',
			quantity: '3',
		});

		// Call method
		component.addPieceItem(0);

		// Check result
		expect(component.pieceItemList.length).toBe(1);
		expect(component.pieceItemList[0].product.description).toBe('New Item');
		expect(component.pieceItemList[0].weight).toBe(15.5);
		expect(component.pieceItemList[0].itemQuantity).toBe(3);

		// Form should be reset
		expect(component.sliPieceItemForm.value.description).toBeNull();
		expect(component.sliPieceItemForm.value.weight).toBeNull();
		expect(component.sliPieceItemForm.value.quantity).toBeNull();
	});

	it('should add piece item to containedPiece when it exists', () => {
		// Set containedPiece
		component.containedPiece = { ...mockPiece };
		component.pieceIndex = 1;

		// Set form values
		component.sliPieceItemForm.setValue({
			description: 'New Contained Item',
			weight: '8.5',
			quantity: '2',
		});

		// Call method
		component.addPieceItem(1);

		// Check result
		expect(component.containedPiece?.containedItems.length).toBe(1);
		expect(component.containedPiece?.containedItems[0].product.description).toBe('New Contained Item');
		expect(component.containedPiece?.containedItems[0].weight).toBe(8.5);
		expect(component.containedPiece?.containedItems[0].itemQuantity).toBe(2);
	});

	it('should not add piece item when form is invalid', () => {
		// Set invalid form value
		component.sliPieceItemForm.setValue({
			description: 'Invalid Item',
			weight: 'abc', // Invalid weight
			quantity: '3',
		});

		// Call method
		component.addPieceItem(0);

		// Check nothing was added
		expect(component.pieceItemList.length).toBe(0);
	});

	it('should delete piece item from pieceItemList', () => {
		// Add items to list
		component.pieceItemList = [{ ...mockPieceItem }, { ...mockPieceItem, product: { description: 'Second Item' } }];

		// Delete first item
		component.delPieceItem(-1, 0);

		// Check result
		expect(component.pieceItemList.length).toBe(1);
		expect(component.pieceItemList[0].product.description).toBe('Second Item');
	});

	it('should delete piece item from containedPiece', () => {
		// Setup containedPiece with items
		component.containedPiece = { ...mockPiece };
		component.containedPiece.containedItems = [
			{ ...mockPieceItem },
			{ ...mockPieceItem, product: { description: 'Second Contained Item' } },
		];
		component.pieceIndex = 1;

		// Delete first item
		component.delPieceItem(1, 0);

		// Check result
		expect(component.containedPiece.containedItems.length).toBe(1);
		expect(component.containedPiece.containedItems[0].product.description).toBe('Second Contained Item');
	});

	it('should emit deletePiece event when delete button is clicked', () => {
		// Setup spy on output
		spyOn(component.deletePiece, 'emit');

		// Setup containedPiece to show delete button
		component.containedPiece = { ...mockPiece };

		// Important: Trigger change detection to update the view with the containedPiece
		fixture.detectChanges();

		// Find delete button - now it should be in the DOM
		const deleteButton = fixture.debugElement.query(By.css('.orll-piece-item-panel__piece-delete-button'));

		// Verify button exists before proceeding
		expect(deleteButton).toBeTruthy();

		// Trigger the click event on the button
		// eslint-disable-next-line @typescript-eslint/no-empty-function
		deleteButton.triggerEventHandler('click', { stopPropagation: () => {} });

		// Check event was emitted
		expect(component.deletePiece.emit).toHaveBeenCalled();
	});
});
