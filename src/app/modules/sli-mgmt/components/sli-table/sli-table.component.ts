import {
	ChangeDetectionStrategy,
	Component,
	EventEmitter,
	Input,
	OnChanges,
	Output,
	SimpleChanges,
	ViewChild,
	AfterViewInit,
} from '@angular/core';
import { MatPaginatorModule, MatPaginatorIntl, PageEvent } from '@angular/material/paginator';
import { CustomPaginatorIntl } from '@shared/services/custom-paginator-intl.service';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { SliListObject } from '../../models/sli-list-object.model';
import { TranslateModule } from '@ngx-translate/core';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatButtonModule } from '@angular/material/button';
import { MatSort, MatSortModule, Sort } from '@angular/material/sort';
import { Router, ActivatedRoute } from '@angular/router';
import { PaginationRequest } from '@shared/models/pagination-request.model';

@Component({
	selector: 'orll-sli-table',
	templateUrl: './sli-table.component.html',
	styleUrls: ['./sli-table.component.scss'],
	changeDetection: ChangeDetectionStrategy.OnPush,
	imports: [MatTableModule, MatSortModule, MatButtonModule, MatMenuModule, MatIconModule, MatPaginatorModule, TranslateModule],
	providers: [{ provide: MatPaginatorIntl, useClass: CustomPaginatorIntl }],
})
export class SliTableComponent implements OnChanges, AfterViewInit {
	@Input() records: SliListObject[] = [];
	@Input() totalRecords = 0;
	@Input() pageParams!: PaginationRequest;
	@Input() fromSli = '';

	@Output() shareSli: EventEmitter<SliListObject> = new EventEmitter<SliListObject>();
	@Output() sortChange = new EventEmitter<Sort>();
	@Output() pagination = new EventEmitter<PageEvent & { sortField?: string; sortDirection?: string }>();

	currentSort: Sort = { active: '', direction: '' };

	readonly displayedColumns: string[] = [
		'waybillNumber',
		'shipper',
		'consignee',
		'goodsDescription',
		'departureLocation',
		'arrivalLocation',
		'createDate',
		'hawbNumber',
		'share',
	];
	readonly tablePageSizes: number[] = [10, 50, 100];

	dataSource = new MatTableDataSource<SliListObject>(this.records || []);

	constructor(
		private readonly router: Router,
		private readonly route: ActivatedRoute
	) {}

	@ViewChild(MatSort) sort!: MatSort;

	ngOnChanges(changes: SimpleChanges): void {
		if (changes['records']) {
			this.dataSource.data = this.records;
			this.dataSource.sort = this.sort;
		}
	}

	ngAfterViewInit() {
		this.sort.sortChange.subscribe((sort: Sort) => {
			this.currentSort = sort;
			this.sortChange.emit(sort);
			this.emitPaginationWithSort();
		});
	}

	private emitPaginationWithSort(event?: PageEvent) {
		const pageEvent = event || {
			pageIndex: this.pageParams.pageNum - 1,
			pageSize: this.pageParams.pageSize,
			length: this.totalRecords,
		};

		this.pagination.emit({
			...pageEvent,
			sortField: this.currentSort.active,
			sortDirection: this.currentSort.direction,
		});
	}

	createSli(): void {
		this.router.navigate(['create'], {
			relativeTo: this.route,
		});
	}

	editSli(waybillNumber: string): void {
		this.router.navigate(['edit', waybillNumber], {
			relativeTo: this.route,
		});
	}

	// eslint-disable-next-line
	trackBySliCode(_index: number, record: SliListObject): string {
		return record.waybillNumber + record.createDate;
	}
}
