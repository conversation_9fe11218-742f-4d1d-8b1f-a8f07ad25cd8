import { ComponentFixture, TestBed } from '@angular/core/testing';

import { LiveAnimalPieceAddComponent } from './live-animal-piece-add.component';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { provideTranslateService } from '@ngx-translate/core';

describe('LiveAnimalPieceAddComponent', () => {
	let component: LiveAnimalPieceAddComponent;
	let fixture: ComponentFixture<LiveAnimalPieceAddComponent>;

	beforeEach(async () => {
		await TestBed.configureTestingModule({
			imports: [LiveAnimalPieceAddComponent],
			providers: [provideHttpClient(withInterceptorsFromDi()), provideHttpClientTesting(), provideTranslateService()],
		}).compileComponents();

		fixture = TestBed.createComponent(LiveAnimalPieceAddComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});
});
