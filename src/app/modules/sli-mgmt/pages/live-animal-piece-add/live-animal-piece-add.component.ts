import { Component } from '@angular/core';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { TranslatePipe } from '@ngx-translate/core';
import { NonNullableFormBuilder, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';

@Component({
	selector: 'orll-live-animal-piece-add',
	imports: [
		CommonModule,
		ReactiveFormsModule,
		TranslatePipe,
		MatFormFieldModule,
		MatIconModule,
		MatInputModule,
		MatExpansionModule,
		MatButtonModule,
		MatSelectModule,
	],
	templateUrl: './live-animal-piece-add.component.html',
	styleUrl: './live-animal-piece-add.component.scss',
})
export class LiveAnimalPieceAddComponent {
	sliLiveAnimalPieceForm = this.fb.group({
		productDescription: [''],
		typeOfPackage: [''],
		packagedIdentifier: [''],
		speciesCommonName: [''],
		speciesScientificName: [''],
		specimenDescription: [''],
		animalQuantity: [''],
		shippingMarks: [''],
		upid: [''],
		grossWeight: [''],
		dimensions: [''],
		whetherHaveDeclaredValueForCustoms: [''],
		whetherHaveDeclaredValueForCarriage: [''],
		textualHandlingInstructions: [''],
	});

	constructor(private readonly fb: NonNullableFormBuilder) {}
}
