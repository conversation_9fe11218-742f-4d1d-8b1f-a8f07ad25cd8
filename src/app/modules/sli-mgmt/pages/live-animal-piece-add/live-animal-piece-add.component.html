<div>
	<h2 class="mat-display-2 orll-sli-shipper-form__title">{{ 'sli.dgPiece.title' | translate }}</h2>
	<form [formGroup]="sliLiveAnimalPieceForm">
		<div class="row">
			<div class="col-6">
				<div class="row">
					<mat-form-field appearance="outline" class="col-12" floatLabel="always">
						<mat-label>{{ 'sli.liveAnimalPiece.formItem.productDescription' | translate }}</mat-label>
						<textarea class="live-animal-piece-form-textarea" matInput formControlName="productDescription"
								  required></textarea>
						@if (sliLiveAnimalPieceForm.get('productDescription')?.hasError('required')) {
							<mat-error>{{ 'validators.required'|translate:{ field: 'sli.liveAnimalPiece.formItem.productDescription' | translate } }}</mat-error>
						}
					</mat-form-field>
				</div>
				<div class="row">
					<mat-form-field appearance="outline" class="col-4" floatLabel="always">
						<mat-label>{{ 'sli.liveAnimalPiece.formItem.typeOfPackage' | translate }}</mat-label>
						<input matInput formControlName="typeOfPackage" required>
						@if (sliLiveAnimalPieceForm.get('typeOfPackage')?.hasError('required')) {
							<mat-error>{{ 'validators.required'|translate:{ field: 'sli.liveAnimalPiece.formItem.typeOfPackage' | translate } }}</mat-error>
						}
					</mat-form-field>
					<mat-form-field appearance="outline" class="col-8" floatLabel="always">
						<mat-label>{{ 'sli.liveAnimalPiece.formItem.packagedIdentifier' | translate }}</mat-label>
						<input matInput formControlName="packagedIdentifier">
						@if (sliLiveAnimalPieceForm.get('packagedIdentifier')?.hasError('required')) {
							<mat-error>{{ 'validators.required'|translate:{ field: 'sli.liveAnimalPiece.formItem.packagedIdentifier' | translate } }}</mat-error>
						}
					</mat-form-field>
				</div>
				<div class="row">
					<div class="col-4">
						<div class="row">
							<mat-form-field appearance="outline" class="col-12" floatLabel="always">
								<mat-label>{{ 'sli.liveAnimalPiece.formItem.speciesCommonName' | translate }}</mat-label>
								<input matInput formControlName="speciesCommonName" required />
								@if (sliLiveAnimalPieceForm.get('speciesCommonName')?.hasError('required')) {
									<mat-error>{{ 'validators.required'|translate:{ field: 'sli.liveAnimalPiece.formItem.speciesCommonName' | translate } }}</mat-error>
								}
							</mat-form-field>
							<mat-form-field appearance="outline" class="col-12" floatLabel="always">
								<mat-label>{{ 'sli.liveAnimalPiece.formItem.speciesScientificName' | translate }}</mat-label>
								<input matInput formControlName="speciesScientificName" required />
								@if (sliLiveAnimalPieceForm.get('speciesScientificName')?.hasError('required')) {
									<mat-error>{{ 'validators.required'|translate:{ field: 'sli.liveAnimalPiece.formItem.speciesScientificName' | translate } }}</mat-error>
								}
							</mat-form-field>
						</div>
					</div>
					<mat-form-field appearance="outline" class="col-8" floatLabel="always">
						<mat-label>{{ 'sli.liveAnimalPiece.formItem.specimenDescription' | translate }}</mat-label>
						<textarea class="live-animal-piece-form-textarea" matInput formControlName="specimenDescription"
								  required></textarea>
						@if (sliLiveAnimalPieceForm.get('specimenDescription')?.hasError('required')) {
							<mat-error>{{ 'validators.required'|translate:{ field: 'sli.liveAnimalPiece.formItem.specimenDescription' | translate } }}</mat-error>
						}
					</mat-form-field>
				</div>
			</div>
			<div class="col-6">
				<div class="row">
					<mat-form-field appearance="outline" class="col-4" floatLabel="always">
						<mat-label>{{ 'sli.liveAnimalPiece.formItem.animalQuantity' | translate }}</mat-label>
						<input matInput formControlName="animalQuantity" required>
						@if (sliLiveAnimalPieceForm.get('animalQuantity')?.hasError('required')) {
							<mat-error>{{ 'validators.required'|translate:{ field: 'sli.liveAnimalPiece.formItem.animalQuantity' | translate } }}</mat-error>
						}
					</mat-form-field>
					<mat-form-field appearance="outline" class="col-4" floatLabel="always">
						<mat-label>{{ 'sli.liveAnimalPiece.formItem.shippingMarks' | translate }}</mat-label>
						<input matInput formControlName="shippingMarks">
						@if (sliLiveAnimalPieceForm.get('shippingMarks')?.hasError('required')) {
							<mat-error>{{ 'validators.required'|translate:{ field: 'sli.liveAnimalPiece.formItem.shippingMarks' | translate } }}</mat-error>
						}
					</mat-form-field>
					<mat-form-field appearance="outline" class="col-4" floatLabel="always">
						<mat-label>{{ 'sli.liveAnimalPiece.formItem.upid' | translate }}</mat-label>
						<input matInput formControlName="upid">
						@if (sliLiveAnimalPieceForm.get('upid')?.hasError('required')) {
							<mat-error>{{ 'validators.required'|translate:{ field: 'sli.liveAnimalPiece.formItem.upid' | translate } }}</mat-error>
						}
					</mat-form-field>
				</div>
				<div class="row">
					<mat-form-field appearance="outline" class="col-4" floatLabel="always">
						<mat-label>{{ 'sli.liveAnimalPiece.formItem.grossWeight' | translate }}</mat-label>
						<input matInput formControlName="grossWeight" required>
						@if (sliLiveAnimalPieceForm.get('grossWeight')?.hasError('required')) {
							<mat-error>{{ 'validators.required'|translate:{ field: 'sli.liveAnimalPiece.formItem.grossWeight' | translate } }}</mat-error>
						}
					</mat-form-field>
					<mat-form-field appearance="outline" class="col-8" floatLabel="always">
						<mat-label>{{ 'sli.liveAnimalPiece.formItem.dimensions' | translate }}</mat-label>
						<input matInput formControlName="dimensions" required>
						@if (sliLiveAnimalPieceForm.get('dimensions')?.hasError('required')) {
							<mat-error>{{ 'validators.required'|translate:{ field: 'sli.liveAnimalPiece.formItem.dimensions' | translate } }}</mat-error>
						}
					</mat-form-field>
				</div>
				<div class="row">
					<mat-form-field appearance="outline" class="col-6" floatLabel="always">
						<mat-label>{{ 'sli.liveAnimalPiece.formItem.whetherHaveDeclaredValueForCustoms' | translate }}</mat-label>
						<mat-select formControlName="whetherHaveDeclaredValueForCustoms">
							<mat-option value="Yes">NCV</mat-option>
							<mat-option value="No">Yes</mat-option>
						</mat-select>
						@if (sliLiveAnimalPieceForm.get('whetherHaveDeclaredValueForCustoms')?.hasError('required')) {
							<mat-error>{{ 'validators.required'|translate:{ field: 'sli.liveAnimalPiece.formItem.whetherHaveDeclaredValueForCustoms' | translate } }}</mat-error>
						}
					</mat-form-field>
					<mat-form-field appearance="outline" class="col-6" floatLabel="always">
						<mat-label>{{ 'sli.liveAnimalPiece.formItem.whetherHaveDeclaredValueForCarriage' | translate }}</mat-label>
						<mat-select formControlName="whetherHaveDeclaredValueForCarriage">
							<mat-option value="Yes">NVD</mat-option>
							<mat-option value="No">Yes</mat-option>
						</mat-select>
						@if (sliLiveAnimalPieceForm.get('whetherHaveDeclaredValueForCarriage')?.hasError('required')) {
							<mat-error>{{ 'validators.required'|translate:{ field: 'sli.liveAnimalPiece.formItem.whetherHaveDeclaredValueForCarriage' | translate } }}</mat-error>
						}
					</mat-form-field>
				</div>
				<div class="row">
					<mat-form-field appearance="outline" class="col-12" floatLabel="always">
						<mat-label>{{ 'sli.liveAnimalPiece.formItem.textualHandlingInstructions' | translate }}</mat-label>
						<textarea class="live-animal-piece-form-textarea" matInput
								  formControlName="textualHandlingInstructions"></textarea>
						@if (sliLiveAnimalPieceForm.get('textualHandlingInstructions')?.hasError('required')) {
							<mat-error>{{ 'validators.required'|translate:{ field: 'sli.liveAnimalPiece.formItem.handlingInstructions' | translate } }}</mat-error>
						}
					</mat-form-field>
				</div>
			</div>
		</div>
	</form>
</div>
