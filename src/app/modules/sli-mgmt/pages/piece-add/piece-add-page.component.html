<div class="orll-piece-add-page">
	<div class="iata-box orll-piece-add-page__box">
		@if (pieceType === 'general') {
			<orll-sli-piece-form></orll-sli-piece-form>
		}
		@if (pieceType === 'general' || pieceType === 'dg') {
			<orll-sli-piece-item [pieceType]="pieceType"></orll-sli-piece-item>
		}
	</div>
	<div class="orll-piece-add-page__footer">
		<div class="orll-piece-add-page__piece-quantity col-12">
			<mat-icon class="close-icon">close</mat-icon>
			<mat-form-field appearance="outline" class="field">
				<mat-label>{{'sli.piece.pieceQuantity' | translate}}</mat-label>
				<input matInput [formControl]="pieceQuantity" placeholder="{{'sli.piece.pieceQuantity' | translate}}">
				@if (pieceQuantity?.hasError('required')) {
					<mat-error>{{'sli.piece.pieceQuantity.required' | translate}}</mat-error>
				}
				@if (pieceQuantity?.hasError('pattern')) {
					<mat-error>{{'sli.mgmt.pieceList.pattern.positiveNumber' | translate}}</mat-error>
				}
			</mat-form-field>
		</div>

		<div class="orll-piece-add-page__buttons col-12">
			<button mat-flat-button (click)="onCancel()" class="orll-piece-add-page__cancel-button">
				{{'sli.mgmt.cancel' | translate}}
			</button>
			<button mat-flat-button color="primary" (click)="onDone()">
				<mat-icon>check</mat-icon>
				{{'sli.piece.done' | translate}}
			</button>
		</div>
	</div>
</div>
