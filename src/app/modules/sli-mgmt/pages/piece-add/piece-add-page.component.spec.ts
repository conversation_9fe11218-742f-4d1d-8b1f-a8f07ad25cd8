import { ComponentFixture, TestBed } from '@angular/core/testing';
// eslint-disable-next-line @typescript-eslint/naming-convention
import PieceAddPageComponent from './piece-add-page.component';
import { ActivatedRoute, ParamMap } from '@angular/router';
import { of } from 'rxjs';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { TranslateModule } from '@ngx-translate/core';
import { ReactiveFormsModule } from '@angular/forms';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { By } from '@angular/platform-browser';
import { SliPieceFormComponent } from '../../components/sli-piece-form/sli-piece-form.component';
import { SliPieceItemComponent } from '../../components/sli-piece-item/sli-piece-item.component';

describe('PieceAddPageComponent', () => {
	let component: PieceAddPageComponent;
	let fixture: ComponentFixture<PieceAddPageComponent>;
	let mockParamMap: jasmine.SpyObj<ParamMap>;
	let mockActivatedRoute: any;

	beforeEach(async () => {
		mockParamMap = jasmine.createSpyObj<ParamMap>('ParamMap', ['get']);
		mockActivatedRoute = {
			snapshot: {
				paramMap: mockParamMap,
			},
			parent: {
				snapshot: {
					url: [],
				},
			},
			queryParams: of({}),
		};

		await TestBed.configureTestingModule({
			imports: [
				PieceAddPageComponent,
				TranslateModule.forRoot(),
				ReactiveFormsModule,
				NoopAnimationsModule,
				MatFormFieldModule,
				MatInputModule,
				MatButtonModule,
				MatIconModule,
				SliPieceFormComponent,
				SliPieceItemComponent,
			],
			providers: [{ provide: ActivatedRoute, useValue: mockActivatedRoute }, provideHttpClient(withInterceptorsFromDi())],
		}).compileComponents();

		fixture = TestBed.createComponent(PieceAddPageComponent);
		component = fixture.componentInstance;
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	it('should initialize with default values', () => {
		// Set up mock return value for paramMap.get
		mockParamMap.get.and.returnValue('general');

		// Initialize component
		component.ngOnInit();
		fixture.detectChanges();

		// Check default values
		expect(component.pieceType).toBe('general');
		expect(component.pieceQuantity.value).toBe(1);
		expect(mockParamMap.get).toHaveBeenCalledWith('pieceType');
	});

	it('should validate pieceQuantity with positive number pattern', () => {
		// Invalid values
		component.pieceQuantity.setValue(Number('abc'));
		expect(component.pieceQuantity.valid).toBeFalsy();
		expect(component.pieceQuantity.hasError('pattern')).toBeTruthy();

		component.pieceQuantity.setValue(0); // Not positive
		expect(component.pieceQuantity.valid).toBeFalsy();
		expect(component.pieceQuantity.hasError('pattern')).toBeTruthy();

		component.pieceQuantity.setValue(-1); // Negative
		expect(component.pieceQuantity.valid).toBeFalsy();
		expect(component.pieceQuantity.hasError('pattern')).toBeTruthy();

		component.pieceQuantity.setValue(1.5); // Decimal
		expect(component.pieceQuantity.valid).toBeFalsy();
		expect(component.pieceQuantity.hasError('pattern')).toBeTruthy();

		// Valid values
		component.pieceQuantity.setValue(1);
		expect(component.pieceQuantity.valid).toBeTruthy();

		component.pieceQuantity.setValue(10);
		expect(component.pieceQuantity.valid).toBeTruthy();
	});

	it('should show error message when pieceQuantity is invalid', () => {
		// Set up mock return value for paramMap.get
		mockParamMap.get.and.returnValue('general');

		// Initialize component
		component.ngOnInit();
		fixture.detectChanges();

		// Set invalid value
		component.pieceQuantity.setValue(null);
		component.pieceQuantity.markAsTouched();
		fixture.detectChanges();

		// Check for required error
		const requiredError = fixture.debugElement.query(By.css('mat-error'));
		expect(requiredError).toBeTruthy();

		// Set invalid pattern
		component.pieceQuantity.setValue(0);
		fixture.detectChanges();

		// Check for pattern error
		const patternError = fixture.debugElement.query(By.css('mat-error'));
		expect(patternError).toBeTruthy();
	});

	it('should have cancel and done buttons', () => {
		// Set up mock return value for paramMap.get
		mockParamMap.get.and.returnValue('general');

		// Initialize component
		component.ngOnInit();
		fixture.detectChanges();

		// Find buttons specifically in the footer section (not including child component buttons)
		const footerButtons = fixture.debugElement.queryAll(By.css('.orll-piece-add-page__buttons button'));
		expect(footerButtons.length).toBe(2);

		// Check cancel button
		const cancelButton = fixture.debugElement.query(By.css('.orll-piece-add-page__cancel-button'));
		expect(cancelButton).toBeTruthy();

		// Check done button
		const doneButton = fixture.debugElement.query(By.css('.orll-piece-add-page__buttons button[color="primary"]'));
		expect(doneButton).toBeTruthy();
	});

	it('should call onCancel when cancel button is clicked', () => {
		// Set up mock return value for paramMap.get
		mockParamMap.get.and.returnValue('general');

		// Initialize component
		component.ngOnInit();
		fixture.detectChanges();

		// Spy on onCancel method
		spyOn(component, 'onCancel');

		// Find and click cancel button
		const cancelButton = fixture.debugElement.query(By.css('.orll-piece-add-page__cancel-button'));
		cancelButton.triggerEventHandler('click', null);

		// Check if onCancel was called
		expect(component.onCancel).toHaveBeenCalled();
	});

	it('should call onDone when done button is clicked', () => {
		// Set up mock return value for paramMap.get
		mockParamMap.get.and.returnValue('general');

		// Initialize component
		component.ngOnInit();
		fixture.detectChanges();

		// Spy on onDone method
		spyOn(component, 'onDone');

		// Find and click done button
		const doneButton = fixture.debugElement.query(By.css('.orll-piece-add-page__buttons button[color="primary"]'));
		doneButton.triggerEventHandler('click', null);

		// Check if onDone was called
		expect(component.onDone).toHaveBeenCalled();
	});
});
