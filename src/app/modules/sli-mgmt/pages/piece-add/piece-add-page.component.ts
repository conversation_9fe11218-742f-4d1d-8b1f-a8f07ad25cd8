import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { DestroyRefComponent } from '@shared/components/destroy-observable/destroy-ref.component';
import { SliPieceFormComponent } from '../../components/sli-piece-form/sli-piece-form.component';
import { SliPieceItemComponent } from '../../components/sli-piece-item/sli-piece-item.component';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { FormControl, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatButtonModule } from '@angular/material/button';

const REGX_POSITIVE_NUMBER = '^[1-9]\\d*$';

@Component({
	selector: 'orll-piece-add-page',
	templateUrl: './piece-add-page.component.html',
	styleUrl: './piece-add-page.component.scss',
	changeDetection: ChangeDetectionStrategy.OnPush,
	imports: [
		MatInputModule,
		MatButtonModule,
		MatIconModule,
		TranslateModule,
		ReactiveFormsModule,
		FormsModule,
		MatFormFieldModule,
		SliPieceFormComponent,
		SliPieceItemComponent,
	],
})
export default class PieceAddPageComponent extends DestroyRefComponent implements OnInit {
	pieceType: string | null = '';
	pieceQuantity = new FormControl<number>(1, [Validators.required, Validators.pattern(REGX_POSITIVE_NUMBER)]);

	constructor(private readonly route: ActivatedRoute) {
		super();
	}

	ngOnInit(): void {
		this.pieceType = this.route.snapshot.paramMap.get('pieceType');
	}

	onCancel(): void {
		// Implement cancel logic here
	}

	onDone(): void {
		// Implement done logic here
	}
}
