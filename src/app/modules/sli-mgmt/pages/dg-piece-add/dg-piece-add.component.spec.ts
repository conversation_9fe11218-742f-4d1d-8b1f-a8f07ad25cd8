import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DgPieceAddComponent } from './dg-piece-add.component';
import { HttpClient, provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { HttpTestingController, provideHttpClientTesting } from '@angular/common/http/testing';
import { provideTranslateService } from '@ngx-translate/core';

describe('DgPieceAddComponent', () => {
	let component: DgPieceAddComponent;
	let fixture: ComponentFixture<DgPieceAddComponent>;
	let httpClient: HttpClient;
	let httpTestingController: HttpTestingController;

	beforeEach(async () => {
		await TestBed.configureTestingModule({
			imports: [DgPieceAddComponent],
			providers: [provideHttpClient(withInterceptorsFromDi()), provideHttpClientTesting(), provideTranslateService()],
		}).compileComponents();

		httpClient = TestBed.inject(HttpClient);
		httpTestingController = TestBed.inject(HttpTestingController);

		fixture = TestBed.createComponent(DgPieceAddComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	afterEach(() => {
		httpTestingController.verify();
	});

	it('should create', () => {
		expect(httpClient).toBeInstanceOf(HttpClient);
		const ctrl = TestBed.inject(HttpTestingController);

		const req = ctrl.expectOne('/prod-api/sys-management/enums/packageType');
		req.flush({
			data: [
				{
					code: '1',
					name: 'Package Type 1',
				},
			],
		});

		expect(component).toBeTruthy();

		expect(component.packagingTypes).toEqual([
			{
				code: '1',
				name: 'Package Type 1',
			},
		])
	});
});
