import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';
import { AbstractControl, NonNullableFormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { TranslatePipe } from '@ngx-translate/core';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { PieceItemPanelComponent } from '../../components/piece-item-panel/piece-item-panel.component';
import { PieceItem } from '../../models/piece/piece-item.model';
import { CodeName } from '@shared/models/code-name.model';
import { SliCreateRequestService } from '../../services/sli-create-request.service';
import { MatAutocompleteModule } from '@angular/material/autocomplete';

@Component({
	selector: 'orll-dg-piece-add',
	imports: [
		CommonModule,
		ReactiveFormsModule,
		TranslatePipe,
		MatFormFieldModule,
		MatIconModule,
		MatInputModule,
		MatExpansionModule,
		MatButtonModule,
		MatSelectModule,
		PieceItemPanelComponent,
		MatAutocompleteModule,
	],
	templateUrl: './dg-piece-add.component.html',
	changeDetection: ChangeDetectionStrategy.OnPush,
	styleUrl: './dg-piece-add.component.scss',
})
export class DgPieceAddComponent implements OnInit {
	sliDgPieceForm = this.fb.group({
		productDescription: ['', Validators.required],
		typeOfPackage: ['', Validators.required],
		packagedIdentifier: [], //optional
		whetherHaveDeclaredValueForCustoms: ['Yes'],
		whetherHaveDeclaredValueForCarriage: ['Yes'],
		specialProvisionId: [],
		explosiveCompatibilityGroupCode: [],
		packagingDangerLevelCode: [],
		technicalName: [],
		unNumber: [],
		shippersDeclaration: [],
		handlingInformation: [],
		allPackedInOne: ['Y'], // optional
		qValueNumeric: [
			'',
			[
				(control: AbstractControl) => {
					if (this.sliDgPieceForm?.get('allPackedInOne')?.value === 'Y') {
						return Validators.required(control);
					}
					return null;
				},
			],
		],
		upid: [], //optional
		shippingMarks: [], //optional
		grossWeight: [],
		dimensions: [],
		hsCommodityDescription: [],
		properShippingName: [],
		textualHandlingInstructions: [],
		hazardClassificationId: [],
		additionalHazardClassificationId: [],
		packingInstructionNumber: [],
		complianceDeclaration: [],
		exclusiveUseIndicator: [],
		authorizationInformation: [],
		aircraftLimitationInformation: [],
	});

	dgPieceItems: PieceItem[] = [];

	packagingTypes: CodeName[] = [];
	filteredPackagingTypes: CodeName[] = [];

	constructor(
		private readonly fb: NonNullableFormBuilder,
		public readonly sliCreateRequestService: SliCreateRequestService
	) {}

	ngOnInit() {
		this.sliCreateRequestService.getPackingTypes().subscribe((packagingTypes: CodeName[]) => {
			this.packagingTypes = packagingTypes;
			this.filteredPackagingTypes = packagingTypes;
		});
	}
}
