import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { map } from 'rxjs/operators';
import { AIRPORTS } from '../../sli-mgmt/ref-data/airports.data';
import { Airport } from '../../sli-mgmt/models/airport.model';
import { HawbListObject } from '../models/hawb-list-object.model';
import { PaginationResponse } from '@shared/models/pagination-response.model';
import { AbstractAutocompleteService } from '@shared/models/autocomplete.model';
import { CodeName } from '@shared/models/code-name.model';
import { ApiService } from '@shared/services/api.service';
import { HttpClient } from '@angular/common/http';
import { HawbSearchPayload } from '../models/hawb-search-payload.model';
import { PaginationRequest } from '@shared/models/pagination-request.model';

@Injectable({ providedIn: 'root' })
export class HawbSearchRequestService extends ApiService implements AbstractAutocompleteService<CodeName> {
	constructor(http: HttpClient) {
		super(http);
	}

	getHawbList(pageParams: PaginationRequest, hawbSearchPayload: HawbSearchPayload): Observable<PaginationResponse<HawbListObject>> {
		return super.getData<PaginationResponse<HawbListObject>>('hawb-management', {
			...pageParams,
			...hawbSearchPayload,
		});
	}

	getOptions(keyword: string, id?: string): Observable<CodeName[]> {
		if (id === 'shipper') {
			return super.getData<{ data: { shipperNameList: string[] } }>('hawb-management/param-query').pipe(
				map((res) => {
					return res.data.shipperNameList
						.filter((shipper: string) => shipper.toLowerCase().includes(keyword.toLowerCase().trim()))
						.map((shipper: string) => {
							return { code: shipper, name: shipper };
						});
				})
			);
		} else if (id === 'consignee') {
			return super.getData<{ data: { consigneeNameList: string[] } }>('hawb-management/param-query').pipe(
				map((res) => {
					return res.data.consigneeNameList
						.filter((consignee: string) => consignee.toLowerCase().includes(keyword.toLowerCase().trim()))
						.map((consignee: string) => {
							return { code: consignee, name: consignee };
						});
				})
			);
		} else if (id === 'airport') {
			return of(AIRPORTS).pipe(
				map((airports: Airport[]) => {
					return airports
						.filter((airport: Airport) => airport.code.toLowerCase().includes(keyword.toLowerCase().trim()))
						.map((airport: Airport) => {
							return { code: airport.code, name: airport.name };
						});
				})
			);
		} else if (id === 'mawbNumber') {
			return super.getData<{ data: { mawbNumberList: string[] } }>('hawb-management/param-query').pipe(
				map((res) => {
					return res.data.mawbNumberList
						.filter((mawbNumber: string) => mawbNumber.toLowerCase().includes(keyword.toLowerCase().trim()))
						.map((mawbNumber: string) => {
							return { code: mawbNumber, name: mawbNumber };
						});
				})
			);
		} else if (id === 'hawbNumber') {
			return super.getData<{ data: { hawbNumberList: string[] } }>('hawb-management/param-query').pipe(
				map((res) => {
					return res.data.hawbNumberList
						.filter((hawbNumber: string) => hawbNumber.toLowerCase().includes(keyword.toLowerCase().trim()))
						.map((hawbNumber: string) => {
							return { code: hawbNumber, name: hawbNumber };
						});
				})
			);
		} else {
			return of([]);
		}
	}
}
