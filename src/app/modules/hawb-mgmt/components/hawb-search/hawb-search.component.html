<div class="orll-hawb-search-form">
	<form [formGroup]="hawbSearchForm">
		<h2 class="mat-display-2 orll-hawb-search-form__title">{{'sli.mgmt.title' | translate}}</h2>
		<div class="row">
			<div class="col-4">
				<mat-form-field appearance="outline" class="width-100" floatLabel="always">
					<mat-icon matPrefix>search</mat-icon>
					<mat-label>{{'sli.mgmt.goodsDescription' | translate}}</mat-label>
					<input matInput formControlName="goodsDescription">
				</mat-form-field>
			</div>

			<div class="col-3">
				<iata-autocomplete
					#shipperAutocomplete
					[id]="'shipper'"
					[api]="hawbSearchRequestService"
					[label]="'sli.mgmt.shipper' | translate"
					[multiple]="true"
					(selected)="selectedItems($event, 'shipper')">
				</iata-autocomplete>
			</div>

			<div class="col-3">
				<iata-autocomplete
					#consigneeAutocomplete
					[id]="'consignee'"
					[api]="hawbSearchRequestService"
					[label]="'sli.mgmt.consignee' | translate"
					[multiple]="true"
					(selected)="selectedItems($event, 'consignee')">
				</iata-autocomplete>
			</div>
		</div>

		<div class="row">
			<div class="col-2">
				<iata-autocomplete
					#departureAutocomplete
					[id]="'airport'"
					[api]="hawbSearchRequestService"
					[label]="'sli.mgmt.departureLocation' | translate"
					[multiple]="true"
					(selected)="selectedItems($event, 'departureLocation')">
				</iata-autocomplete>
			</div>

			<div class="col-2">
				<iata-autocomplete
					#arrivalAutocomplete
					[id]="'airport'"
					[api]="hawbSearchRequestService"
					[label]="'sli.mgmt.arrivalLocation' | translate"
					[multiple]="true"
					(selected)="selectedItems($event, 'arrivalLocation')">
				</iata-autocomplete>
			</div>

			<div class="col-3">
				<iata-autocomplete
					#hawbNumberAutocomplete
					[id]="'hawbNumber'"
					[api]="hawbSearchRequestService"
					[label]="'hawb.mgmt.hawbNumber' | translate"
					[multiple]="true"
					(selected)="selectedItems($event, 'hawbNumber')">
				</iata-autocomplete>
			</div>

			<div class="col-3">
				<mat-form-field appearance="outline" class="width-100" floatLabel="always">
					<mat-icon matPrefix>search</mat-icon>
					<mat-label>{{'sli.mgmt.createDate' | translate}}</mat-label>
					<mat-date-range-input [rangePicker]="createDateRangePicker">
						<input matStartDate formControlName="startDate" placeholder="Start date">
						<input matEndDate formControlName="endDate" placeholder="End date">
					</mat-date-range-input>
					<mat-datepicker-toggle matIconSuffix [for]="createDateRangePicker"></mat-datepicker-toggle>
					<mat-date-range-picker #createDateRangePicker></mat-date-range-picker>
				</mat-form-field>
			</div>

			<div class="orll-hawb-search-form__footer col-2">
				<button mat-stroked-button color="primary" (click)="onReset($event)" class="orll-hawb-search-form__reset-button">
					<mat-icon>refresh</mat-icon>
					{{'sli.mgmt.reset' | translate}}
				</button>
				<button mat-flat-button color="primary" [disabled]="hawbSearchForm.invalid" (click)="onSearch()">
					<mat-icon>search</mat-icon>
					{{'sli.mgmt.search' | translate}}
				</button>
			</div>
		</div>
	</form>
</div>
