import {
	ChangeDetectionStrategy,
	Component,
	EventEmitter,
	Input,
	OnChanges,
	Output,
	SimpleChanges,
	ViewChild,
	AfterViewInit,
} from '@angular/core';
import { MatPaginatorModule, MatPaginatorIntl, PageEvent } from '@angular/material/paginator';
import { CustomPaginatorIntl } from '@shared/services/custom-paginator-intl.service';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { HawbListObject } from '../../models/hawb-list-object.model';
import { TranslateModule } from '@ngx-translate/core';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatButtonModule } from '@angular/material/button';
import { MatSort, MatSortModule, Sort } from '@angular/material/sort';
import { Router, ActivatedRoute } from '@angular/router';
import { PaginationRequest } from '@shared/models/pagination-request.model';

@Component({
	selector: 'orll-hawb-table',
	templateUrl: './hawb-table.component.html',
	styleUrls: ['./hawb-table.component.scss'],
	changeDetection: ChangeDetectionStrategy.OnPush,
	imports: [MatTableModule, MatSortModule, MatButtonModule, MatMenuModule, MatIconModule, MatPaginatorModule, TranslateModule],
	providers: [{ provide: MatPaginatorIntl, useClass: CustomPaginatorIntl }],
})
export class HawbTableComponent implements OnChanges, AfterViewInit {
	@Input() records: HawbListObject[] = [];
	@Input() totalRecords = 0;
	@Input() pageParams!: PaginationRequest;

	@Output() shareHawb: EventEmitter<HawbListObject> = new EventEmitter<HawbListObject>();
	@Output() sortChange = new EventEmitter<Sort>();
	@Output() pagination = new EventEmitter<PageEvent & { sortField?: string; sortDirection?: string }>();

	currentSort: Sort = { active: '', direction: '' };

	readonly displayedColumns: string[] = [
		'hawbNumber',
		'shipper',
		'consignee',
		'goodsDescription',
		'origin',
		'destination',
		'pieceQuantity',
		'createDate',
		'mawbNumber',
		'share',
	];
	readonly tablePageSizes: number[] = [10, 50, 100];

	dataSource = new MatTableDataSource<HawbListObject>(this.records || []);

	constructor(
		private readonly router: Router,
		private readonly route: ActivatedRoute
	) {}

	@ViewChild(MatSort) sort!: MatSort;

	ngOnChanges(changes: SimpleChanges): void {
		if (changes['records']) {
			this.dataSource.data = this.records;
			this.dataSource.sort = this.sort;
		}
	}

	ngAfterViewInit() {
		this.sort.sortChange.subscribe((sort: Sort) => {
			this.currentSort = sort;
			this.sortChange.emit(sort);
			this.emitPaginationWithSort();
		});
	}

	private emitPaginationWithSort(event?: PageEvent) {
		const pageEvent = event || {
			pageIndex: this.pageParams.pageNum - 1,
			pageSize: this.pageParams.pageSize,
			length: this.totalRecords,
		};

		this.pagination.emit({
			...pageEvent,
			sortField: this.currentSort.active,
			sortDirection: this.currentSort.direction,
		});
	}

	createHawbFromSli(): void {
		this.router.navigate(['create', 'fromSli'], {
			relativeTo: this.route,
		});
	}

	editHawb(hawbId: string): void {
		this.router.navigate(['edit', hawbId], {
			relativeTo: this.route,
		});
	}

	// eslint-disable-next-line
	trackByHawbId(_index: number, record: HawbListObject): string {
		return record.hawbId + record.createDate;
	}
}
