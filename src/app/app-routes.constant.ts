import { Routes } from '@angular/router';
import { CanDeactivateGuard } from '@shared/services/can-deactivate.guard';

export const ROUTES: Routes = [
	{
		path: 'sli',
		data: {
			breadcrumb: {
				label: 'sli.mgmt.list',
			},
		},
		children: [
			{
				path: '',
				loadComponent: () => import('./modules/sli-mgmt/pages/sli-list/sli-list-page.component'),
			},
			{
				path: 'create',
				data: {
					breadcrumb: {
						label: 'sli.mgmt.create',
					},
				},
				children: [
					{
						path: '',
						loadComponent: () => import('./modules/sli-mgmt/pages/sli-create/sli-create-page.component'),
						canDeactivate: [CanDeactivateGuard],
					},
					{
						path: 'piece/:pieceType',
						loadComponent: () => import('./modules/sli-mgmt/pages/piece-add/piece-add-page.component'),
						data: {
							breadcrumb: {
								label: 'sli.piece.add',
							},
						},
					},
				],
			},
			{
				path: 'edit/:sliNumber',
				loadComponent: () => import('./modules/sli-mgmt/pages/sli-create/sli-create-page.component'),
				data: {
					breadcrumb: {
						label: 'sli.mgmt.edit',
					},
				},
			},
		],
	},
	{
		path: 'hawb',
		data: {
			breadcrumb: {
				label: 'hawb.mgmt.title',
			},
		},
		children: [
			{
				path: '',
				loadComponent: () => import('./modules/hawb-mgmt/pages/hawb-list/hawb-list-page.component'),
			},
			{
				path: 'create/:fromSli',
				data: {
					breadcrumb: {
						label: 'hawb.mgmt.create.fromSli',
					},
				},
				children: [
					{
						path: '',
						loadComponent: () => import('./modules/sli-mgmt/pages/sli-list/sli-list-page.component'),
					},
				],
			},
		],
	},

	{ path: '**', redirectTo: 'sli' },
] as Routes;
