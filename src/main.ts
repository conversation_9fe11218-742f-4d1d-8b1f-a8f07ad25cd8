import { importProvidersFrom, inject, provideAppInitializer } from '@angular/core';
import { AppComponent } from './app/app.component';
import { DEFAULT_LANG } from '@shared/global.constants';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
import { TranslateModule, TranslateLoader } from '@ngx-translate/core';
import { provideAnimations } from '@angular/platform-browser/animations';
import { BrowserModule, bootstrapApplication } from '@angular/platform-browser';
import { withInterceptorsFromDi, provideHttpClient, HttpClient, HTTP_INTERCEPTORS } from '@angular/common/http';
import { RouterModule } from '@angular/router';
import { ROUTES } from './app/app-routes.constant';
import { AuthInterceptor } from '@shared/auth/auth-interceptor';
import { MAT_RADIO_DEFAULT_OPTIONS } from '@angular/material/radio';
import { AppInitializerService } from '@shared/services/app-initializer.service';

bootstrapApplication(AppComponent, {
	providers: [
		importProvidersFrom(
			BrowserModule,
			RouterModule.forRoot(ROUTES, { useHash: true }),
			TranslateModule.forRoot({
				loader: {
					provide: TranslateLoader,
					useFactory: (http: HttpClient) => new TranslateHttpLoader(http),
					deps: [HttpClient],
				},
				defaultLanguage: DEFAULT_LANG,
			})
		),
		{
			provide: HTTP_INTERCEPTORS,
			useClass: AuthInterceptor,
			multi: true,
		},
		{
			provide: MAT_RADIO_DEFAULT_OPTIONS,
			useValue: { color: 'primary' },
		},
		provideAppInitializer(() => {
			const initializerFn = (
				(appInitService: AppInitializerService) => () =>
					appInitService.initializeApp()
			)(inject(AppInitializerService));
			return initializerFn();
		}),
		provideAnimations(),
		provideHttpClient(withInterceptorsFromDi()),
	],
}).catch((err) => console.error(err));
